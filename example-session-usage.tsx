'use client';

import { useSession } from "next-auth/react";
import { useEffect } from "react";

export default function ExampleComponent() {
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === "loading") {
      console.log("Loading session...");
      return;
    }

    if (status === "unauthenticated") {
      console.log("User not authenticated");
      return;
    }

    if (session?.user) {
      console.log("User session:", {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        accessToken: session.user.accessToken,
        systemBrandCode: session.user.systemBrandCode,
        contentLanguage: session.user.contentLanguage
      });
    }
  }, [session, status]);

  // Alternative: Using getSession() function
  const handleGetSession = async () => {
    const { getSession } = await import("next-auth/react");
    const currentSession = await getSession();

    if (currentSession?.user) {
      console.log("Session from getSession():", currentSession.user);
    }
  };

  // Loading state
  if (status === "loading") {
    return <div>Loading...</div>;
  }

  // Not authenticated
  if (status === "unauthenticated") {
    return <div>Please sign in</div>;
  }

  // Authenticated
  return (
    <div>
      <h1>Welcome, {session?.user?.name}!</h1>
      <p>Access Token: {session?.user?.accessToken}</p>
      <p>Brand Code: {session?.user?.systemBrandCode}</p>
      <p>Language: {session?.user?.contentLanguage}</p>
    </div>
  );
}
