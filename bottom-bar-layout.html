<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bottom Bar Layout</title>
    <!-- Bootstrap 3 CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .bottom-bar {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .left-section {
            display: flex;
            align-items: center;
        }
        
        .right-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .delete-icon {
            color: #999;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .delete-icon:hover {
            color: #d9534f;
        }
        
        .required-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .required-text {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #5cb85c;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .menu-icon {
            color: #999;
            font-size: 16px;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .menu-icon:hover {
            color: #666;
        }
        
        /* Alternative Bootstrap version */
        .bootstrap-bottom-bar {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 30px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .bootstrap-bottom-bar .row {
            margin: 0;
        }
        
        .bootstrap-bottom-bar .col-md-6 {
            padding: 0;
        }
    </style>
</head>
<body>
    <!-- Method 1: Flexbox Layout -->
    <div class="bottom-bar">
        <div class="left-section">
            <span class="delete-icon">
                <i class="glyphicon glyphicon-trash"></i>
            </span>
        </div>
        
        <div class="right-section">
            <div class="required-section">
                <span class="required-text">Required</span>
                <label class="toggle-switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                </label>
            </div>
            
            <span class="menu-icon">
                <i class="glyphicon glyphicon-menu-hamburger"></i>
            </span>
        </div>
    </div>
    
    <!-- Method 2: Bootstrap Grid Layout -->
    <div class="container-fluid">
        <div class="bootstrap-bottom-bar">
            <div class="row">
                <div class="col-md-6">
                    <span class="delete-icon">
                        <i class="glyphicon glyphicon-trash"></i>
                    </span>
                </div>
                <div class="col-md-6 text-right">
                    <div class="required-section" style="display: inline-flex;">
                        <span class="required-text">Required</span>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <span class="menu-icon" style="margin-left: 15px;">
                        <i class="glyphicon glyphicon-menu-hamburger"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Example with multiple bars -->
    <div style="margin-top: 30px;">
        <h4>Multiple Bottom Bars Example:</h4>
        
        <div class="bottom-bar" style="margin-bottom: 10px;">
            <div class="left-section">
                <span class="delete-icon">
                    <i class="glyphicon glyphicon-trash"></i>
                </span>
            </div>
            <div class="right-section">
                <div class="required-section">
                    <span class="required-text">Required</span>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                <span class="menu-icon">
                    <i class="glyphicon glyphicon-menu-hamburger"></i>
                </span>
            </div>
        </div>
        
        <div class="bottom-bar" style="margin-bottom: 10px;">
            <div class="left-section">
                <span class="delete-icon">
                    <i class="glyphicon glyphicon-trash"></i>
                </span>
            </div>
            <div class="right-section">
                <div class="required-section">
                    <span class="required-text">Required</span>
                    <label class="toggle-switch">
                        <input type="checkbox">
                        <span class="slider"></span>
                    </label>
                </div>
                <span class="menu-icon">
                    <i class="glyphicon glyphicon-menu-hamburger"></i>
                </span>
            </div>
        </div>
    </div>

    <!-- Bootstrap 3 JS -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    
    <script>
        // Toggle switch functionality
        $('.toggle-switch input').change(function() {
            if ($(this).is(':checked')) {
                console.log('Required: ON');
            } else {
                console.log('Required: OFF');
            }
        });
        
        // Delete icon click
        $('.delete-icon').click(function() {
            if (confirm('Are you sure you want to delete this item?')) {
                $(this).closest('.bottom-bar, .bootstrap-bottom-bar').fadeOut();
            }
        });
        
        // Menu icon click
        $('.menu-icon').click(function() {
            console.log('Menu clicked');
            // Add your menu functionality here
        });
    </script>
</body>
</html>
