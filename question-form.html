<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Form</title>
    <!-- Bootstrap 3 CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <style>
        .form-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .question-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #fff;
        }
        .language-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .required-toggle {
            float: right;
        }
        .add-question-btn {
            color: #5cb85c;
            cursor: pointer;
            font-size: 16px;
        }
        .add-question-btn:hover {
            color: #449d44;
        }
        .delete-btn {
            color: #d9534f;
            cursor: pointer;
            font-size: 18px;
            float: left;
            margin-top: 10px;
        }
        .delete-btn:hover {
            color: #c9302c;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #5cb85c;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .right-panel {
            border-left: 1px solid #ddd;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="form-container">
            <div class="row">
                <!-- Left Panel - Question Form -->
                <div class="col-md-8">
                    <div class="question-card">
                        <div class="row">
                            <!-- Left Panel - Question Form -->
                            <div class="col-md-7">
                                <h4>Question 1</h4>

                                <!-- Question Section -->
                                <div class="form-group">
                                    <div class="language-label">EN</div>
                                    <input type="text" class="form-control" placeholder="Enter Question">
                                </div>

                                <div class="form-group">
                                    <div class="language-label">TH</div>
                                    <input type="text" class="form-control" placeholder="กรอกคำถาม">
                                </div>

                                <!-- Answer Section -->
                                <div class="form-group">
                                    <label class="language-label">Answer</label>
                                    <div class="language-label">EN</div>
                                    <textarea class="form-control" rows="2" placeholder="Enter answer" style="background-color: #f5f5f5;" readonly></textarea>
                                </div>

                                <div class="form-group">
                                    <div class="language-label">TH</div>
                                    <textarea class="form-control" rows="2" placeholder="กรุณากรอกข้อมูลที่ถูกต้อง" style="background-color: #f5f5f5;" readonly></textarea>
                                </div>

                                <!-- Error Message Section -->
                                <div class="form-group">
                                    <label class="language-label">Error Message</label>
                                    <div class="language-label">EN</div>
                                    <input type="text" class="form-control" placeholder="Enter Error Message">
                                </div>

                                <div class="form-group">
                                    <div class="language-label">TH</div>
                                    <input type="text" class="form-control" placeholder="กรอกข้อความ">
                                </div>
                            </div>

                            <!-- Right Panel - Settings -->
                            <div class="col-md-5 right-panel">
                                <!-- Type Dropdown -->
                                <div class="form-group" style="margin-top: 40px;">
                                    <label class="language-label">Type</label>
                                    <select class="form-control">
                                        <option>Email</option>
                                        <option>Text</option>
                                        <option>Number</option>
                                        <option>Phone</option>
                                    </select>
                                </div>

                                <!-- Answer Placeholder Section -->
                                <div class="form-group" style="margin-top: 30px;">
                                    <label class="language-label">Answer Placeholder</label>
                                    <div class="language-label">EN</div>
                                    <input type="text" class="form-control" placeholder="Enter placeholder">
                                </div>

                                <div class="form-group">
                                    <div class="language-label">TH</div>
                                    <input type="text" class="form-control" placeholder="กรุณากรอกข้อความ">
                                </div>

                                <!-- Required Toggle -->
                                <div class="form-group" style="margin-top: 50px;">
                                    <div class="pull-right">
                                        <span style="margin-right: 10px; font-weight: normal;">Required</span>
                                        <label class="toggle-switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>

                                <!-- Menu Icon -->
                                <div class="form-group">
                                    <div class="pull-right" style="margin-top: 10px;">
                                        <span style="font-size: 18px; color: #999;">
                                            <i class="glyphicon glyphicon-menu-hamburger"></i>
                                        </span>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Delete Button -->
                        <div class="row">
                            <div class="col-md-12">
                                <span class="delete-btn">
                                    <i class="glyphicon glyphicon-trash"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Add Question Button -->
                    <div class="text-left">
                        <span class="add-question-btn">
                            <i class="glyphicon glyphicon-plus-sign"></i> Add Question
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 3 JS -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    
    <script>
        // Add Question functionality
        $('.add-question-btn').click(function() {
            // Clone the question card and update question number
            var questionCard = $('.question-card').first().clone();
            var questionCount = $('.question-card').length + 1;
            questionCard.find('h4').text('Question ' + questionCount);
            
            // Clear all input values
            questionCard.find('input, textarea').val('');
            
            // Insert before the add button
            questionCard.insertBefore($(this).parent());
        });
        
        // Delete Question functionality
        $(document).on('click', '.delete-btn', function() {
            if ($('.question-card').length > 1) {
                $(this).closest('.question-card').remove();
                
                // Update question numbers
                $('.question-card').each(function(index) {
                    $(this).find('h4').text('Question ' + (index + 1));
                });
            } else {
                alert('Cannot delete the last question');
            }
        });
    </script>
</body>
</html>
