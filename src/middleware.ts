import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
// import { getToken } from "next-auth/jwt";

export async function middleware(request: NextRequest) {
  const token = request.nextUrl.searchParams.get("token");
  // const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  // If no token in query params, continue normally
  if (!token) {
    return NextResponse.next();
  }

  try {
    // console.log('callbackUrl A:',request.nextUrl.toString())
    // const callbackUrl = encodeURIComponent(request.nextUrl.toString());
    // console.log('callbackUrl B:',callbackUrl)
    // console.log('Request URl:',request.url)
    // console.log('Request URl Origin:',request.nextUrl.origin)

    // const baseOrigin = process.env.NEXTAUTH_URL;

    const rawCallback = request.nextUrl.pathname + request.nextUrl.search;
    const callbackUrl = encodeURIComponent(`${process.env.NEXT_PUBLIC_BASE_URL}${rawCallback}`);
    const redirectUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/signin?callbackUrl=${callbackUrl}`;

    return NextResponse.redirect(
      new URL(`/api/auth/signin?callbackUrl=${callbackUrl}`, redirectUrl),
    );
  } catch (error) {
    console.error("Auth middleware error:", error);
    return NextResponse.redirect(new URL("/auth/error", request.url));
  }
}

export const config = {
  // Apply this middleware to all routes except API routes and static files
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};
