"use client";

import React, { createContext, useContext, useState } from "react";
import type { ReactNode } from "react";
import { ErrorModal } from "./ErrorModal"; // ตัว modal ที่คุณมี

interface ErrorState {
  isOpen: boolean;
  title: string;
  description: string;
  redirectTo?: string;
  showRedirectButton?: boolean;
  redirectButtonText?: string;
  cancelButtonText?: string;
}

type ShowErrorArgs = Omit<ErrorState, "isOpen">;

interface ErrorModalContextType {
  showError: (args: ShowErrorArgs) => void;
  closeError: () => void;
}

const ErrorModalContext = createContext<ErrorModalContextType | undefined>(undefined);

export const useErrorModal = () => {
  const context = useContext(ErrorModalContext);
  if (!context) throw new Error("useErrorModal must be used within ErrorModalProvider");
  return context;
};

export const ErrorModalProvider = ({ children }: { children: ReactNode }) => {
  const [errorModal, setErrorModal] = useState<ErrorState>({
    isOpen: false,
    title: "",
    description: "",
    redirectTo: "",
    showRedirectButton: false,
    redirectButtonText: "",
    cancelButtonText: "",
  });

  const showError = ({
    title,
    description,
    redirectTo = "",
    showRedirectButton = false,
    redirectButtonText = "",
    cancelButtonText = "",
  }: ShowErrorArgs) => {
    setErrorModal({
      isOpen: true,
      title,
      description,
      redirectTo,
      showRedirectButton,
      redirectButtonText,
      cancelButtonText,
    });
  };

  const closeError = () => {
    setErrorModal((prev) => ({ ...prev, isOpen: false }));
  };

  return (
    <ErrorModalContext.Provider value={{ showError, closeError }}>
      {children}
      <ErrorModal
        isOpen={errorModal.isOpen}
        onClose={closeError}
        title={errorModal.title}
        description={errorModal.description}
        redirectTo={errorModal.redirectTo}
        showRedirectButton={errorModal.showRedirectButton}
        redirectButtonText={errorModal.redirectButtonText}
        cancelButtonText={errorModal.cancelButtonText}
      />
    </ErrorModalContext.Provider>
  );
};