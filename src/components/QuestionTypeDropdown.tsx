'use client';

import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { 
  Mail, 
  AlignJustify, 
  AlignLeft, 
  CheckSquare, 
  Circle, 
  ChevronDown as CaretDown, 
  Calendar, 
  Clock, 
  CalendarDays, 
  MapPin 
} from 'lucide-react';

interface QuestionType {
  QuestionTypeID: number;
  QuestionGroup: string;
  QuestionType: string;
  QuestionTypeName: string;
  QuestionIcon: string;
  QuestionTypeStatus: string;
}

interface QuestionTypeDropdownProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

const questionTypes: QuestionType[] = [
  {
    "QuestionTypeID": 1,
    "QuestionGroup": "Contact Info",
    "QuestionType": "Email",
    "QuestionTypeName": "Email",
    "QuestionIcon": "<i class=\"fa-solid fa-envelope\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 2,
    "QuestionGroup": "Text",
    "QuestionType": "ShortText",
    "QuestionTypeName": "Short Text",
    "QuestionIcon": "<i class=\"fa-solid fa-align-justify\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 3,
    "QuestionGroup": "Text",
    "QuestionType": "Paragraph",
    "QuestionTypeName": "Paragraph",
    "QuestionIcon": "<i class=\"fa-solid fa-align-left\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 4,
    "QuestionGroup": "Choice",
    "QuestionType": "Checkbox",
    "QuestionTypeName": "Checkboxes",
    "QuestionIcon": "<i class=\"fa-regular fa-square-check\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 5,
    "QuestionGroup": "Choice",
    "QuestionType": "Radiobutton",
    "QuestionTypeName": "Radio Buttons",
    "QuestionIcon": "<i class=\"fa-regular fa-circle-dot\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 6,
    "QuestionGroup": "Choice",
    "QuestionType": "Dropdown",
    "QuestionTypeName": "Dropdown",
    "QuestionIcon": "<i class=\"fa-solid fa-caret-down\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 7,
    "QuestionGroup": "Other",
    "QuestionType": "Date",
    "QuestionTypeName": "Date",
    "QuestionIcon": "<i class=\"fa-solid fa-calendar-days\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 8,
    "QuestionGroup": "Other",
    "QuestionType": "Time",
    "QuestionTypeName": "Time",
    "QuestionIcon": "<i class=\"fa-regular fa-clock\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 9,
    "QuestionGroup": "Other",
    "QuestionType": "DateTime",
    "QuestionTypeName": "Date&Time",
    "QuestionIcon": "<i class=\"fa-solid fa-calendar\"></i>",
    "QuestionTypeStatus": "Active"
  },
  {
    "QuestionTypeID": 10,
    "QuestionGroup": "Other",
    "QuestionType": "Location",
    "QuestionTypeName": "Location",
    "QuestionIcon": "<i class=\"fa-solid fa-location-dot\"></i>",
    "QuestionTypeStatus": "Active"
  }
];

const getIcon = (questionType: string) => {
  const iconMap: Record<string, React.ReactNode> = {
    'Email': <Mail className="w-4 h-4" />,
    'ShortText': <AlignJustify className="w-4 h-4" />,
    'Paragraph': <AlignLeft className="w-4 h-4" />,
    'Checkbox': <CheckSquare className="w-4 h-4" />,
    'Radiobutton': <Circle className="w-4 h-4" />,
    'Dropdown': <CaretDown className="w-4 h-4" />,
    'Date': <CalendarDays className="w-4 h-4" />,
    'Time': <Clock className="w-4 h-4" />,
    'DateTime': <Calendar className="w-4 h-4" />,
    'Location': <MapPin className="w-4 h-4" />
  };
  
  return iconMap[questionType] || <Circle className="w-4 h-4" />;
};

export default function QuestionTypeDropdown({ 
  value, 
  onChange, 
  placeholder = "Select question type" 
}: QuestionTypeDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Group questions by QuestionGroup
  const groupedQuestions = questionTypes.reduce((groups, question) => {
    const group = question.QuestionGroup;
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(question);
    return groups;
  }, {} as Record<string, QuestionType[]>);

  const selectedQuestion = questionTypes.find(q => q.QuestionType === value);

  const handleSelect = (questionType: string) => {
    onChange?.(questionType);
    setIsOpen(false);
  };

  return (
    <div className="relative w-full">
      {/* Dropdown Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-4 py-3 bg-white border border-gray-300 rounded-lg text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        <div className="flex items-center space-x-3">
          {selectedQuestion ? (
            <>
              {getIcon(selectedQuestion.QuestionType)}
              <span className="text-gray-900">{selectedQuestion.QuestionTypeName}</span>
            </>
          ) : (
            <span className="text-gray-500">{placeholder}</span>
          )}
        </div>
        <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-80 overflow-y-auto">
          {Object.entries(groupedQuestions).map(([groupName, questions]) => (
            <div key={groupName}>
              {/* Group Header */}
              <div className="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-50 border-b border-gray-200">
                {groupName}
              </div>
              
              {/* Group Options */}
              {questions.map((question) => (
                <button
                  key={question.QuestionTypeID}
                  type="button"
                  onClick={() => handleSelect(question.QuestionType)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                    value === question.QuestionType ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                  }`}
                >
                  <div className={`${value === question.QuestionType ? 'text-blue-600' : 'text-gray-500'}`}>
                    {getIcon(question.QuestionType)}
                  </div>
                  <span className="font-medium">{question.QuestionTypeName}</span>
                </button>
              ))}
            </div>
          ))}
        </div>
      )}

      {/* Overlay to close dropdown when clicking outside */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
