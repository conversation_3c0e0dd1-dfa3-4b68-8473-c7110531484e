import { useSession } from "next-auth/react";
import { useMemo } from "react";

export const useAuth = () => {
  const { data: session, status } = useSession();

  const user = useMemo(() => {
    if (!session?.user) return null;
    
    return {
      id: session.user.id,
      name: session.user.name,
      email: session.user.email,
      accessToken: session.user.accessToken,
      systemBrandCode: session.user.systemBrandCode,
      contentLanguage: session.user.contentLanguage,
    };
  }, [session]);

  return {
    user,
    session,
    isLoading: status === "loading",
    isAuthenticated: status === "authenticated",
    isUnauthenticated: status === "unauthenticated",
    accessToken: session?.user?.accessToken,
    systemBrandCode: session?.user?.systemBrandCode,
    contentLanguage: session?.user?.contentLanguage,
  };
};
