
'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, PlusCircle, ArrowLeft, Plus} from 'lucide-react';
import Image from 'next/image';
// import Link from "next/link";
// import { useTransition } from 'react';
// import { useRouter } from "next/navigation";
import { api } from "@/trpc/react";
import { Input } from '@/components/ui/input';
import { useTranslation } from '@/contexts/LanguageContext';
import { useSession } from "next-auth/react";

interface CardItem {
    cardID: number;
    cardNumber: string;
    cardType: string;
    expireDate: string;
    isDefault: boolean;
}

export default function CheckoutPayment() 
{
    const { t } = useTranslation();

    const { data: session, status } = useSession();
    const systemBrandCode = session?.user.systemBrandCode.toLowerCase() ?? "";

    // const [selectedCard, setSelectedCard] = useState<string>('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const goBackToWebApp = () => {
        // window.location.assign("/checkout");
    };

    const goToAddCard = () => {
        setIsSubmitting(true)
        window.location.assign("/addcard");
    };

    const handleEditCard = (cardID: number) => {
        window.location.assign(`/mycarddetail/${cardID}`);
    };

    // const handleNext = () => {

    //     const selectedCardObj = cardList?.cards.find(card => card.cardNumber === selectedCard);
    //     setIsSubmitting(true)
    //     window.location.assign(`/checkout?cardID=${selectedCardObj?.cardID}&cardNumber=${selectedCardObj?.cardNumber}&cardType=${selectedCardObj?.cardType}&expireDate=${selectedCardObj?.expireDate}&isDefault=${selectedCardObj?.isDefault}`);
    // };

    // const updateDefaultCard = api.card.updateDefaultCreditCard.useMutation({
    //     onSuccess: async (data) => {
    //         console.log(data.newAccessToken);
    //         if(data.newAccessToken){
    //             await fetch('/api/set-token', {
    //                 method: 'POST',
    //                 credentials: 'include',
    //                 body: JSON.stringify({ token: data.newAccessToken }),
    //             })
    //                 .then(res => res.json())
    //                 .then(data => {
    //                     console.log('[Set Token DDD]', data);
                        
    //                 })
    //                 .catch(err => {
    //                     setIsSubmitting(false);
    //                     console.error('[Set Token Error]', err);
    //                 });

    //             // const result = await res.json();
    //             // router.push("/checkout");
    //             window.location.assign("/checkout");
    //             // redirect('/checkout');
    //         }
    //         // router.push("/checkout");
    //     }
    // });

    const { data: cardList, isLoading, error } = api.card.getCreditCardList.useQuery();

    // Token update effect
    useEffect(() => {
        if (cardList?.newAccessToken) {
            fetch('/api/set-token', {
                method: 'POST',
                credentials: 'include',
                body: JSON.stringify({ token: cardList.newAccessToken }),
            })
                .then(res => res.json())
                .then(data => {
                    console.log('[Set Token]', data);
                })
                .catch(err => console.error('[Set Token Error]', err));
        }
    }, [cardList?.newAccessToken]);

    useEffect(() => {
        if (cardList?.cards && cardList.cards.length > 0) {
          localStorage.setItem('noFirstCard', '1');
        } else {
          localStorage.setItem('noFirstCard', '0');
        }
      }, [cardList]);

    // Debug output
    useEffect(() => {
        if (error) {
            console.error('API Error:', error);
        }
    }, [error]);

    // useEffect(() => {
    //     if (cardList?.cards && cardList.cards.length > 0) {
    //         // Find the default card
    //         const defaultCard = cardList.cards.find(card => card.isDefault);
    //         if (defaultCard) {
    //             setSelectedCard(defaultCard.cardNumber);
    //         } else if (cardList.cards[0]) {
    //             // If no default card, select the first one
    //             setSelectedCard(cardList.cards[0].cardNumber);
    //         }
    //     }
    // }, [cardList?.cards]);

    if(isLoading || status === 'loading') {
        return (
            <div className="flex flex-col min-h-screen bg-white animate-pulse">
                {/* Header Skeleton */}
                <div className="flex items-center justify-center p-4 relative">
                    <div className="absolute top-4 left-4">
                        <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                    </div>
                    <div className="h-7 w-36 bg-gray-200 rounded mt-1"></div>
                </div>

                <div className="px-4 mt-6 flex-1 overflow-auto">
                    <div className="space-y-4 mb-14">
                        {/* Card Skeletons */}
                        {[1, 2].map((item) => (
                        <div key={item} className="p-4 rounded-lg border border-gray-200">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="flex items-center">
                                        <div className="w-10 h-8 bg-gray-200 rounded-md mr-3"></div>
                                        <div className="h-5 w-40 bg-gray-200 rounded"></div>
                                    </div>
                                </div>
                                <div className="h-6 w-6 bg-gray-200 rounded-full"></div>
                            </div>
                        </div>
                        ))}
                    </div>

                    {/* Add Card Link Skeleton */}
                    <div className="flex items-center py-2">
                        <div className="h-6 w-6 bg-gray-200 rounded-full mr-2"></div>
                        <div className="h-5 w-48 bg-gray-200 rounded"></div>
                        <div className="h-5 w-5 bg-gray-200 rounded-full ml-auto"></div>
                    </div>
                </div>

                {/* Footer Skeleton */}
                <footer className="fixed bottom-0 left-0 w-full bg-white p-4">
                    <div className="h-14 w-full bg-gray-200 rounded-lg"></div>
                </footer>
            </div>
        )
    }

    const handleGoBackToLineLiff = () => {
        window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
    };
    
    if(error){
        return (  
            <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
                <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
                    <div className="flex items-center justify-center mb-10">
                        <Image src="/images/package/mascot-sad.svg" width={100} height={100} alt="Loading animation"></Image>
                    </div>

                    <div className="text-center space-y-4 mb-8">
                        <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>

                        <div className="space-y-1">
                            <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
                        </div>
                    </div>
                </div>

                <div className="w-full max-w-md space-y-4 mb-8">
                    <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={handleGoBackToLineLiff}>
                        {t('back_to_home')}
                    </Button>
                </div>
            </div>
        )
    }

    return (
        <>
            <div className="flex flex-col min-h-screen bg-white">
                {/* Header */}
                <div className="flex items-center justify-center p-4 relative text-gray-700">
                    <div className="absolute top-4 left-4">
                        <ArrowLeft className="h-6 w-6 mt-2" onClick={() => goBackToWebApp()} />
                    </div>
                    <h1 className="text-base font-medium mt-1">{t('my_credit_cards')}</h1>
                </div>

                <div className="px-4 mt-6 flex-1 overflow-auto">
                    <h2 className="text-base font-medium mb-4">My Cards</h2>

                    <div className="space-y-4 mb-14">

                        {cardList?.cards && cardList.cards.length > 0 ? (
                            cardList.cards.map((card: CardItem) => (
                                <div
                                    key={card.cardID}
                                    className={`p-4 rounded-lg border border-gray-200`}
                                >
                                    <label className="flex items-center justify-between cursor-pointer" onClick={() => handleEditCard(card.cardID)} >
                                        <div className="flex items-center space-x-3">
                                            <div className="flex items-center">
                                                <div className="w-10 h-8 rounded-md flex items-center justify-center mr-3">
                                                    <Image
                                                        src={`/images/credit-card/${card.cardType.toLowerCase()}.png`}
                                                        alt={card.cardType}
                                                        width={40}
                                                        height={40}
                                                        className="rounded"
                                                    />
                                                </div>
                                                <span className="text-sm">{card.cardNumber}</span>
                                                {card.isDefault && (
                                                    <span className="ml-3 px-3 py-1 text-alert-label bg-alert-label-background rounded-full text-xs">
                                                        {t('default_card')}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                         <ChevronLeft className="h-5 w-5 ml-auto rotate-180" />
                                    </label>
                                </div>
                            ))
                        ) : (
                            <div className="text-center py-4 text-gray-500">
                                {/* No cards found. Add a card to get started. */}
                            </div>
                        )}
                    </div>

                    {/* </Button> */}
                    {!isLoading && cardList?.cards && cardList.cards.length < 3 && (
                        <>
                            <h2 className="text-base font-medium mb-4">Add New Card</h2>

                            <Button
                                className="w-full bg-tertiary hover:bg-tertiary text-white py-6 text-lg rounded-lg disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden h-14"
                                style={{
                                    backgroundImage: "url('/images/add-card-icon.svg')",
                                    backgroundRepeat: "no-repeat",
                                    backgroundPosition: "right 0px bottom -20px",
                                    backgroundSize: "80px 80px"
                                }}
                                disabled={isSubmitting}
                                onClick={goToAddCard}
                            >
                                {isSubmitting ? (
                                    <div className="flex items-center justify-center">
                                        <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading" />
                                    </div>
                                ) : (
                                    <>
                                        {/* <span>{t('next')}</span> */}                               
                                        {/* <Plus className="text-white h-4 w-4" /> */}
                                        <Image src={`/images/mycardlist/${systemBrandCode}/plus-icon.svg`} width={20} height={20} alt="plus-icon" />
                                        <span className="text-sm">Add credit card</span>
                                    </>
                                )}
                            </Button>
                        </>                  
                    )}                   
                </div>               
            </div>
        </>
    );
}