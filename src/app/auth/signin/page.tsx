"use client";

import { signIn, signOut } from "next-auth/react";
import { useEffect } from "react";
import { useDerivedCallbackUrl } from "./_hook/use-derived-callback-url";
import { redirectAction } from "./_action/redirect-after-signin-action";
import { getSession } from "next-auth/react";

export default function SignInPage() {
  const { token, langCode, callbackUrl } = useDerivedCallbackUrl();
  let redirectUrl = "/";
  console.log('🔐 Token signin:',token);
  // console.log('CallbackUrl signin:',callbackUrl);

  const authenticate = async () => {
    try {
      await signOut({ redirect: false });
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const result = await signIn("token-auth", {
        token: token,
        langCode: langCode,
        redirect: false, // Prevent NextAuth from handling the redirect
        // callbackUrl: `/packagelist?token=${token}`,
      });

      if (result?.error) {
        redirectUrl = "/auth/error";
        return;
      }

      const session = await getSession();
      const accessToken = session?.user?.accessToken;

      if (accessToken) {
        await fetch("/api/set-token", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ token: accessToken }),
        });
      }

      if (callbackUrl) {
        redirectUrl = callbackUrl;
        return;
      }
    } catch {
      redirectUrl = "/auth/error";
      return;
    } finally {
      await redirectAction(redirectUrl);
    }
  };

  useEffect(() => {
    void authenticate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  
  return (
    ""
    // <div className="flex min-h-screen flex-col items-center justify-center">
    //   <h1 className="text-2xl font-bold">Signing In...</h1>
    //   <p className="mt-4">Please wait while we authenticate you.</p>
    // </div>
  );
}