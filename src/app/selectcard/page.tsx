'use client';

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslation } from '@/contexts/LanguageContext';

interface CreditCard {
  id: string;
  cardNumber: string;
  cardType: string;
  balance: string;
  isSelected?: boolean;
}

export default function SelectCardPage() {
  const { t } = useTranslation();
  const router = useRouter();
  
  // State management
  const [selectedCardId, setSelectedCardId] = useState<string>('1');
  const [pageLoading, setPageLoading] = useState(true);
  
  // Mock credit card data - replace with actual API call
  const [creditCards] = useState<CreditCard[]>([
    {
      id: '1',
      cardNumber: '2341 6534 2391',
      cardType: 'visa',
      balance: '3,472.00'
    },
    {
      id: '2',
      cardNumber: '6278 3792 4281',
      cardType: 'mastercard',
      balance: '0.00'
    }
  ]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Event handlers
  const handleCardSelect = (cardId: string) => {
    setSelectedCardId(cardId);
  };

  const handleConfirm = () => {
    // Pass selected card back to previous page or navigate
    router.back();
  };

  const getCardIcon = (cardType: string) => {
    switch (cardType.toLowerCase()) {
      case 'visa':
        return '/images/credit-card/visa.png';
      case 'mastercard':
        return '/images/credit-card/mastercard.png';
      default:
        return '/images/credit-card/default.png';
    }
  };

  return (
    <>
      {pageLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation" />
          </div>
        </div>
      )}

      <div className="flex flex-col min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-[#DDF1F0] flex items-center justify-center p-4 relative text-gray-700">
          <div className="absolute top-4 left-4">
            <ArrowLeft className="h-6 w-6 mt-2 cursor-pointer" onClick={() => router.back()} />
          </div>
          <h1 className="text-lg font-medium mt-1">Select card</h1>
        </div>

        <div className="flex flex-col p-6 space-y-6 flex-1">
          {/* Top up to Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-800">Top up to</h2>
            
            {/* Credit Cards List */}
            <div className="space-y-4">
              {creditCards.map((card) => (
                <div
                  key={card.id}
                  className={`bg-white rounded-lg p-4 border-2 cursor-pointer transition-all ${
                    selectedCardId === card.id
                      ? 'border-orange-400 bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleCardSelect(card.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {/* Radio Button */}
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        selectedCardId === card.id
                          ? 'border-orange-500 bg-orange-500'
                          : 'border-gray-300'
                      }`}>
                        {selectedCardId === card.id && (
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        )}
                      </div>
                      
                      {/* Card Image */}
                      <div className="w-16 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-lg flex items-center justify-center relative overflow-hidden">
                        {card.cardType === 'visa' ? (
                          <div className="w-12 h-8 bg-white rounded flex items-center justify-center">
                            <span className="text-blue-600 font-bold text-xs">VISA</span>
                          </div>
                        ) : (
                          <div className="w-12 h-8 bg-gray-800 rounded flex items-center justify-center">
                            <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                              <div className="w-3 h-3 bg-gray-800 rounded-full"></div>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* Card Details */}
                      <div>
                        <p className="text-gray-500 text-sm">{card.cardNumber}</p>
                        <p className="text-lg font-semibold">
                          {card.balance} <span className="text-sm font-normal text-gray-500">THB</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-white p-4 border-t border-gray-200">
          <Button
            onClick={handleConfirm}
            className="w-full bg-quaternary text-white py-6 text-lg rounded-lg"
          >
            OK
          </Button>
        </footer>
      </div>
    </>
  );
}
