"use client";
import React, { useEffect, useState } from 'react';
import { ArrowLeft } from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useRouter } from "next/navigation";
import { useParams } from 'next/navigation';
import { api } from "@/trpc/react";
import { useTranslation } from '@/lib/useTranslation';

export default function PacekageProductDetailScreen() 
{
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState('details');
    // const [isLoading, setIsLoading] = useTransition();

    const router = useRouter();
    const params = useParams();
    const productId = params.id;
    // const router = useRouter();
    // const { id } = router.query;
    // console.log(productId);

    const goBackToPackageDetail = () => {
        // console.log(shopProductID)
        router.back();
    };
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = `${date.getMonth() + 1}`.padStart(2, "0");
        const day = `${date.getDate()}`.padStart(2, "0");
        return `${day}/${month}/${year}`;
    };

    const { data: couponDetail, isLoading, error } = api.package.getPackageProductDetail.useQuery({ shopProductID: productId as string });

    useEffect(() => {
        const setToken = async () => {
            if (couponDetail?.newAccessToken) {
                try {
                const res = await fetch('/api/set-token', {
                    method: 'POST',
                    credentials: 'include',
                    body: JSON.stringify({ token: couponDetail.newAccessToken }),
                });
                const data = await res.json() as { message: string };
                console.log('[Set Token Success]', data);
                } catch (err) {
                console.error('[Set Token Error]', err);
                }
            }
        };  
        void setToken();
    }, [couponDetail?.newAccessToken]);

    const handleGoBackToLineLiff = () => {
        window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
    };

    if(error){
        return (  
            <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
                <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
                    <div className="flex items-center justify-center mb-10">
                        <Image src="/images/package/mascot-sad.svg" width={100} height={100} alt="Loading animation"></Image>
                    </div>

                    <div className="text-center space-y-4 mb-8">
                        <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>

                        <div className="space-y-1">
                            <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
                            {/* <p className="text-gray-400">please try again</p> */}
                        </div>
                    </div>
                </div>

                <div className="w-full max-w-md space-y-4 mb-8">
                    <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={handleGoBackToLineLiff}>
                        {t('back_to_home')}
                    </Button>
                </div>
            </div>
        )
    }

    if(isLoading) {
        return (
        <div className="flex items-center justify-center w-full bg-yellow-50 animate-pulse">
            <div className="w-full max-w-md rounded-lg overflow-hidden h-screen">
            {/* Back Button Skeleton */}
            <div className="w-10 h-10 p-2 m-2 bg-gray-300 rounded-full"></div>

            {/* Coupon Image Skeleton */}
            <div className="w-full p-6">
                <div className="w-full h-56 bg-gray-300 rounded-lg"></div>
            </div>

            {/* Coupon Details Skeleton */}
            <div className="bg-gray-100 mt-6 p-2 rounded-lg">
                <div className="pb-0 text-center py-6 p-3">
                {/* Title Skeleton */}
                <div className="h-8 bg-gray-300 rounded-lg w-48 mx-auto"></div>
                
                {/* Validity Box Skeleton */}
                <div className="bg-white border border-gray-200 p-4 pt-2 rounded-lg mt-5">
                    <div className="text-center mt-2">
                    <div className="h-4 bg-gray-300 rounded w-24 mx-auto mb-2"></div>
                    <div className="h-6 bg-gray-300 rounded w-32 mx-auto mb-2"></div>
                    <div className="h-4 bg-gray-300 rounded w-40 mx-auto"></div>
                    </div>
                </div>
                </div>

                {/* Tabs Skeleton */}
                <div className="mt-6 p-4">
                {/* Tab Headers Skeleton */}
                <div className="flex w-full border-gray-200">
                    <div className="w-1/2 text-center py-3">
                    <div className="h-5 bg-gray-300 rounded w-28 mx-auto"></div>
                    </div>
                    <div className="w-1/2 text-center py-3">
                    <div className="h-5 bg-gray-300 rounded w-36 mx-auto"></div>
                    </div>
                </div>

                {/* Tab Content Skeleton */}
                <div className="p-2 mt-4">
                    <div className="space-y-3">
                    <div className="h-4 bg-gray-300 rounded w-full"></div>
                    <div className="h-4 bg-gray-300 rounded w-full"></div>
                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-300 rounded w-full mt-4"></div>
                    <div className="space-y-2 mt-2">
                        <div className="h-4 bg-gray-300 rounded w-full"></div>
                        <div className="h-4 bg-gray-300 rounded w-5/6"></div>
                        <div className="h-4 bg-gray-300 rounded w-4/6"></div>
                        <div className="h-4 bg-gray-300 rounded w-5/6"></div>
                    </div>
                    </div>
                </div>
                </div>
            </div>
            </div>
        </div>
        )
    }

    return (
        <div className="flex items-center justify-center w-full bg-yellow-50">
            <div className="w-full max-w-md rounded-lg overflow-hidden">

                {/* Back Button */}
                <div className="w-10 p-2 m-2 bg-gray-500 rounded-full text-white" >
                    <ArrowLeft className="" onClick={goBackToPackageDetail}/>
                </div>

                {/* Coupon Image */}
                <div className="w-full pt-2 pl-8 pr-8 pb-2">
                    {
                        couponDetail?.data?.image ? (
                            <img
                                src={couponDetail.data.image}
                                alt="Coupon image"
                                className="w-full h-56 object-fill rounded-lg"
                            />
                        ) : (
                            <div className="w-full h-56 bg-gray-300 flex items-center justify-center rounded-lg">
                                <span className="text-gray-600">{t('no_image')}</span>
                            </div>
                        )
                    }

                </div>

                {/* Coupon Details */}
                <div className="bg-gray-100 mt-6 p-2 rounded-lg">
                    <div className="pb-0 text-center py-4 p-3">
                        <h2 className="text-lg font-bold break-words">{couponDetail?.data.title}</h2>
                        <div className="bg-white border border-gray-200 p-4 text-sm pt-2 rounded-lg mt-5">
                            <div className="text-gray-400 text-center mt-2">
                                {couponDetail?.data.validAge === 'Unlimit' ? (
                                    <>
                                        <span className="text-xs">{t('valid_until')}</span>
                                        <br />
                                        <span className="text-sm font-medium text-green-600">{formatDate(couponDetail?.data.validUntil)}</span>
                                    </>
                                ) : (
                                    <>
                                        <span className="text-xs">{t('valid_for')}</span>
                                        <br />
                                        <span className="text-sm font-medium text-green-600">
                                            {couponDetail?.data.validAge.includes('D') 
                                                ? couponDetail?.data.validAge.replace('D', ' '+t('days')) 
                                                : couponDetail?.data.validAge.includes('M') 
                                                    ? couponDetail?.data.validAge.replace('M', ' '+t('months')) 
                                                    : couponDetail?.data.validAge.replace('Y', ' '+t('years'))}
                                        </span>
                                        <span className="text-sm text-gray-400">&nbsp;{t('after_purchase')}</span>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Custom Tabs - No borders */}
                    <div className="mt-2 p-4 h-screen">
                        {/* Tab Headers */}
                        <div className="flex w-full border-gray-200">

                            <button onClick={() => setActiveTab('details')} className={`w-1/2 text-center text-sm py-3 font-medium relative ${ activeTab === 'details' ? 'text-gray-600' : 'text-gray-400'}`}>
                                {t('coupon_detail')}
                                <div className={`absolute bottom-0 left-0 w-full h-1 transition-all duration-200 ${ activeTab === 'details' ? 'bg-[#E98875]' : 'bg-[#C4C4C4]' }`} />
                            </button>

                            {/* Tab: Terms */}
                            <button
                            onClick={() => setActiveTab('terms')}
                            className={`w-1/2 text-center text-sm py-3 font-medium relative ${ activeTab === 'terms' ? 'text-gray-600' : 'text-gray-400' }`}>
                                {t('term_and_conditions')}
                                <div className={`absolute bottom-0 left-0 w-full h-1 transition-all duration-200 ${ activeTab === 'terms' ? 'bg-[#E98875]' : 'bg-[#C4C4C4]'}`} />
                            </button>

                        </div>

                        {/* Tab Content */}
                        {activeTab === 'details' && (
                            <div className="p-2 mt-4">
                                <div className="space-y-3 text-sm">
                                    <p className="text-gray-700" style={{ whiteSpace: 'pre-line' }}>
                                        {couponDetail?.data.description}
                                    </p>
                                    
                                </div>
                            </div>
                        )}

                        {activeTab === 'terms' && (
                            <div className="p-2 mt-4">
                                <div className="text-sm text-gray-700 space-y-2">
                                <p className="text-gray-700" style={{ whiteSpace: 'pre-line' }}>
                                        {couponDetail?.data.detail}
                                    </p>
                                </div>
                            </div>
                        )}
                        <div className="mt-30"></div>
                    </div>                  
                </div>
                
            </div>
        </div>
    );
};