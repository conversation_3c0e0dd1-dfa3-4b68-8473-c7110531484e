

'use client';

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { ArrowLeft, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import * as Switch from '@radix-ui/react-switch';
import { z } from "zod";
import { api } from "@/trpc/react";
// import { ErrorModal } from '@/components/ErrorModal';
import { useErrorModal } from "@/components/ErrorModalContext";
import { useTranslation } from '@/contexts/LanguageContext';
import { useSession } from "next-auth/react";

interface CardFormData {
  cardNumber: string;
  cardHolder: string;
  expiry: string;
  cvv: string;
  isDefaultCard: boolean;
}

interface CardValidationErrors {
  cardNumber: string | null;
  cardHolder: string | null;
  expiry: string | null;
  cvv: string | null;
}

interface TokenResponse {
  id: string;
  object: string;
  livemode: boolean;
  location: string;
  created: string;
  used: boolean;
  card: {
    object: string;
    id: string;
    livemode: boolean;
    location: string;
    created: string;
  };
}

export default function AddCardPage() {

  const { t } = useTranslation();
  const { data: session, status } = useSession();
  const systemBrandCode = session?.user.systemBrandCode.toLowerCase() ?? "";
  // const { language, setLanguage } = useLanguage();

  // State management
  const [formData, setFormData] = useState<CardFormData>({
    cardNumber: '',
    cardHolder: '',
    expiry: '',
    cvv: '',
    isDefaultCard: false
  });

  const [errors, setErrors] = useState<CardValidationErrors>({
    cardNumber: null,
    cardHolder: null,
    expiry: null,
    cvv: null
  });

  const [cardType, setCardType] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isCvvModalOpen, setIsCvvModalOpen] = useState<boolean>(false);
  const { showError } = useErrorModal();
  const [noFirstCard, setNoFirstCard] = useState<string | null>(null);
  const [pageLoading, setPageLoading] = useState(true);

  const router = useRouter();
  const createTokenMutation = api.card.createToken.useMutation();
  const addCreditCardMutation = api.card.addCreditCard.useMutation();

    useEffect(() => {
      const noFirstCard = localStorage.getItem('noFirstCard');
      setNoFirstCard(noFirstCard); 

      const timer = setTimeout(() => {
        setPageLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }, []);

  // Utility functions
  const getCardType = (cardNumber: string): string => {
    const cleaned = cardNumber.replace(/\D/g, '');
    if (cleaned.startsWith('4') && (cleaned.length === 13 || cleaned.length === 16)) return 'visa';
    if ((/^5[1-5]/.test(cleaned) && cleaned.length === 16) || (/^2[2-7]/.test(cleaned) && cleaned.length === 16)) return 'mastercard';

    // JCB
    if (/^35(?:2[89]|[3-8]\d)/.test(cleaned) && cleaned.length >= 16 && cleaned.length <= 19) return 'jcb';
    
    // UnionPay
    if (cleaned.startsWith('62') && cleaned.length >= 16 && cleaned.length <= 19) return 'unionpay';
    
    // Maestro
    if ((/^(5018|5020|5038|5893|6304|6759|6761|6762|6763)/.test(cleaned) || 
        /^(6)(?!1)/.test(cleaned)) && 
        cleaned.length >= 12 && cleaned.length <= 19) return 'maestro';
    return '';
  };

  const formatCardNumber = (value: string): string => {
    const formattedValue = value.replace(/\D/g, "");
    return formattedValue.replace(/(\d{4})(?=\d)/g, "$1 ");
  };

  const validationSchemas = {
    cardNumber: z.string()
      .nonempty(t("card_number_required"))
      .regex(
        /^(?:\d{4} \d{4} \d{4} \d{4} \d{3}|\d{4} \d{4} \d{4} \d{4}|\d{4} \d{4} \d{4} \d{3}|\d{4} \d{4} \d{4} \d{2})$/,
        t("card_number_invalid")
      ),
    cardHolder: z.string()
      .nonempty(t("card_holder_name_required"))
      .regex(/^[A-Za-z\s]+$/, t("card_holder_name_invalid")),
    expiry: z.string()
      .nonempty(t("expiry_date_required"))
      .regex(
        /^(0[1-9]|1[0-2])\/\d{2}$/,
        t("expiry_date_invalid")
      ),
    cvv: z.string()
      .nonempty(t("cvv_required"))
      .regex(/^\d{3}$/, t("cvv_invalid")),
  };

  // Validation functions
  const validateField = (field: keyof Omit<CardFormData, 'isDefaultCard'>, value: string) => {
    try {
      validationSchemas[field].parse(value);
      setErrors(prev => ({ ...prev, [field]: null }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors(prev => ({ ...prev, [field]: error.errors[0]?.message }));
      }
    }
  };

  // Event handlers
  const handleInputChange = (field: keyof Omit<CardFormData, 'isDefaultCard'>) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    let formattedValue = value;

    if (field === 'cardNumber') {
      formattedValue = formatCardNumber(value);
      setCardType(getCardType(formattedValue));
    } else if (field === 'expiry') {
      // Remove any non-digit characters
      const cleaned = value.replace(/\D/g, '');
      
      // Format as MM/YY
      if (cleaned.length <= 2) {
        formattedValue = cleaned;
      } else {
        formattedValue = `${cleaned.substring(0, 2)}/${cleaned.substring(2, 4)}`;
      }
    }

    setFormData(prev => ({ ...prev, [field]: formattedValue }));
    validateField(field, formattedValue);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      const [month, year] = formData.expiry?.split('/') ?? [];
      if (!month || !year) {
        throw new Error('Invalid expiry date format. Expected MM/YY.');
      }
      const tokenResponse = await createTokenMutation.mutateAsync({
        name: formData.cardHolder,
        number: formData.cardNumber.replace(/\s/g, ''),
        expiration_month: parseInt(month),
        expiration_year: parseInt(year),
        security_code: formData.cvv
      }) as TokenResponse;

      const addCardResponse = await addCreditCardMutation.mutateAsync({
        token: tokenResponse.id,
        isDefault: formData.isDefaultCard,
        noFirstCard: noFirstCard == "1" ? "1" : "0",
      });

      if (addCardResponse.newAccessToken) {
        await fetch('/api/set-token', {
          method: 'POST',
          credentials: 'include',
          body: JSON.stringify({ token: addCardResponse.newAccessToken }),
        });
      }

      // console.log(addCardResponse);
      // console.log(addCardResponse.data.code);
      if(addCardResponse.data.code === "1") {
        window.location.href = addCardResponse.data.data.authorizeURI;
      } else {
        // setModalOpen(true);
        showError({
          title: t('unable_to_process_payment'),
          description: t('check_your_payment_details'),
          showRedirectButton: false,
          redirectButtonText: "",
          redirectTo: "/home",
          cancelButtonText: t('done'),
        });
      }

    } catch (error) {
      // console.log('Error:');
      console.log(error)
      // setModalOpen(true);
      showError({
        title: t('unable_to_process_payment'),
        description: t('payment_provider_connection_error'),
        showRedirectButton: false,
        cancelButtonText: t('cancel'),
        redirectButtonText: t('done'),
        redirectTo: "",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const CvvInfoModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
    if (!isOpen) return null;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm backdrop-blur-sm animate-in fade-in duration-200"
          onClick={onClose}
        />

        {/* Modal content */}
        <div className="bg-white rounded-2xl p-6 w-[90%] max-w-md relative z-50 shadow-xl">


          <div className="flex items-center justify-center">
            <Image
              src={`/images/package/${systemBrandCode}/cvv-icon.svg`}
              alt="CVV Icon"
              width={80}
              height={80}
              className="object-contain"
            />
          </div>

          <h3 className="text-xl font-semibold text-center my-4">{t('cvv')}</h3>

          {/* Description */}
          <div className="space-y-2 text-center mb-4">
            <p className="text-gray-400 text-md">{t('it_is_the_three_digit_number')}</p>
            {/* <p className="text-gray-400 text-md">usually found on the back of credit card</p> */}
          </div>

          {/* Done Button */}
          <Button onClick={onClose} className=" w-full bg-tertiary text-white py-6 rounded-md mt-4 text-lg">
            {t('done')}
          </Button>
        </div>
      </div>
    );
  };

  return (
    <>
      {pageLoading || status === 'loading' && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation" />
          </div>
        </div>
      )}

      <div className="flex flex-col min-h-screen bg-white">
        {/* Header */}
        <div className="flex items-center justify-center p-4 relative text-gray-700">
          <div className="absolute top-4 left-4">
            <ArrowLeft className="h-6 w-6 mt-2" onClick={() => router.back()} />
          </div>
          <h1 className="text-base font-medium mt-1">{t('add_credit_debit_card')}</h1>
        </div>

        <div className="flex flex-col p-6 space-y-6">
          {/* Card Number Input */}
          <div className="space-y-2">
            <Label htmlFor="cardNumber" className="text-gray-700 text-sm font-medium">
              {t('credit_card_number')}
            </Label>
            <div className="relative">
              <Input
                id="cardNumber"
                name="cardNumber"
                value={formData.cardNumber}
                onChange={handleInputChange('cardNumber')}
                className={`bg-gray-10 mt-2 p-4 text-sm rounded-md border ${errors.cardNumber ? 'border-red-500' : 'border-gray-200'} h-12`}
                placeholder="0000 0000 0000 0000"
                maxLength={19}
                inputMode="numeric"
                disabled={isSubmitting}
                autoComplete="cc-number"
              />
              {cardType && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Image
                    src={`/images/credit-card/${(cardType).toLowerCase()}.png`}
                    alt={cardType}
                    width={40}
                    height={40}
                    className="rounded" 
                  />
                </div>
              )}
            </div>
            {errors.cardNumber && <p className="text-xs text-red-500">{errors.cardNumber}</p>}
          </div>

          {/* Card Holder Input */}
          <div className="space-y-2">
            <Label htmlFor="cardHolder" className="text-gray-700 text-sm font-medium">
              {t('card_holder_name')}
            </Label>
            <Input
              id="cardHolder"
              name="cardHolder"
              value={formData.cardHolder}
              onChange={handleInputChange('cardHolder')}
              className={`bg-gray-10 mt-2 p-4 text-sm rounded-md border ${errors.cardHolder ? 'border-red-500' : 'border-gray-200'} h-12`}
              placeholder={t('card_holder_name')}
              disabled={isSubmitting}
              autoComplete="cc-name"
            />
            {errors.cardHolder && <p className="text-xs text-red-500">{errors.cardHolder}</p>}
          </div>

          {/* Expiry and CVV */}
          <div className="flex gap-4">
            <div className="flex-1 space-y-2">
              <Label htmlFor="expiry" className="text-gray-700 text-sm font-medium">
                {t('expiry_date')}
              </Label>
              <Input
                id="expiry"
                name="expiry"
                value={formData.expiry}
                onChange={handleInputChange('expiry')}
                className={`bg-gray-10 p-4 mt-2 text-sm rounded-md border ${errors.expiry ? 'border-red-500' : 'border-gray-200'} h-12`}
                placeholder="MM/YY"
                disabled={isSubmitting}
                autoComplete="cc-exp"
              />
              {errors.expiry && <p className="text-xs text-red-500">{errors.expiry}</p>}
            </div>

            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <Label htmlFor="cvv" className="text-gray-700 text-sm font-medium">
                  {t('cvv')}
                </Label>
                <Info
                  className="h-5 w-5 text-gray-400 cursor-pointer"
                  onClick={() => setIsCvvModalOpen(true)}
                />
              </div>
              <Input
                id="cvv"
                name="cvv"
                type="text"
                value={formData.cvv}
                onChange={handleInputChange('cvv')}
                className={`bg-gray-10 p-4 text-sm rounded-md border mt-3 ${errors.cvv ? 'border-red-500' : 'border-gray-200'} h-12`}
                placeholder="000"
                maxLength={3}
                disabled={isSubmitting}
                inputMode="numeric"
                autoComplete="cc-csc"
              />
              {errors.cvv && <p className="text-xs text-red-500">{errors.cvv}</p>}
            </div>
          </div>

          {/* Default Card Switch */}
          {noFirstCard === '1' && (
            <>
              <div className="flex items-center justify-between pt-4">
                <Label htmlFor="isDefaultCard" className="text-gray-700 text-sm font-medium">
                  {t('set_as_default_card')}
                </Label>
                <Switch.Root
                  className="w-16 h-9 bg-gray-300 rounded-full relative data-[state=checked]:bg-quaternary"
                  checked={formData.isDefaultCard}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isDefaultCard: checked }))}
                  disabled={isSubmitting}
                >
                  <Switch.Thumb className="block w-7 h-7 bg-white rounded-full transition-transform translate-x-1 data-[state=checked]:translate-x-8 data-[disabled]:opacity-40" />
                </Switch.Root>
              </div>
            </>
          )}
          <div className="mt-2 p-4 bg-gray-100 text-gray-400 text-xs rounded-lg">
            {t('we_may_charge_your_card')}
          </div>
          
        </div>

        {/* Footer */}
        <footer className="fixed bottom-0 left-0 w-full bg-white p-4">
          <Button
            onClick={handleSubmit}
            className="w-full bg-tertiary text-white py-6 text-lg rounded-lg disabled:opacity-50"
            disabled={
              isSubmitting ||
              !formData.cardNumber ||
              !formData.cardHolder ||
              !formData.expiry ||
              !formData.cvv ||
              Boolean(errors.cardNumber) ||
              Boolean(errors.cardHolder) ||
              Boolean(errors.expiry) ||
              Boolean(errors.cvv)
            }
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading animation" />
              </div>
            ) : (
              t('add_card')
            )}
          </Button>
        </footer>
      </div>

      {/* CVV Info Modal */}
      <CvvInfoModal isOpen={isCvvModalOpen} onClose={() => setIsCvvModalOpen(false)} />
    </>
  );
}