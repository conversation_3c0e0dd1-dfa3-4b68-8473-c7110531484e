'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Trash2 } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from "next/navigation";
import * as Switch from '@radix-ui/react-switch';
import { useTranslation } from '@/contexts/LanguageContext';

interface CreditCard {
    id: string;
    cardNumber: string;
    cardType: string;
    expireDate: string;
    isDefault: boolean;
    isExpired?: boolean;
}

export default function MyCreditCardsPage() {
    const { t } = useTranslation();
    const router = useRouter();
    const [pageLoading, setPageLoading] = useState(true);
    const [isDefaultCardToggling, setIsDefaultCardToggling] = useState(false);
    
    // Mock credit card data - replace with actual API call
    const [creditCard] = useState<CreditCard>({
        id: '1',
        cardNumber: '**** **** **** 1000',
        cardType: 'visa',
        expireDate: '28/08/2026',
        isDefault: false,
        isExpired: true
    });

    useEffect(() => {
        const timer = setTimeout(() => {
            setPageLoading(false);
        }, 500);
        return () => clearTimeout(timer);
    }, []);

    const handleDefaultCardToggle = async (checked: boolean) => {
        setIsDefaultCardToggling(true);
        try {
            // Add your API call here to update default card status
            console.log('Setting default card:', checked);
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.error('Error updating default card:', error);
        } finally {
            setIsDefaultCardToggling(false);
        }
    };

    const handleDeleteCard = () => {
        // Show confirmation dialog and handle card deletion
        if (confirm('Are you sure you want to delete this card?')) {
            console.log('Deleting card...');
            // Add your delete API call here
        }
    };

    const formatCardNumber = (cardNumber: string) => {
        return cardNumber;
    };

    const getCardTypeIcon = (cardType: string) => {
        switch (cardType.toLowerCase()) {
            case 'visa':
                return 'VISA';
            case 'mastercard':
                return 'MC';
            default:
                return cardType.toUpperCase();
        }
    };

    const isCardExpired = (expireDate: string) => {
        const [day, month, year] = expireDate.split('/');
        const cardExpiry = new Date(parseInt(`20${year}`), parseInt(month) - 1, parseInt(day));
        return cardExpiry < new Date();
    };

    return (
        <>
            {pageLoading && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
                    <div className="rounded-full">
                        <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation" />
                    </div>
                </div>
            )}

            <div className="flex flex-col min-h-screen bg-gray-50">
                {/* Header */}
                <div className="bg-white flex items-center justify-center p-4 relative text-gray-700 border-b border-gray-200">
                    <div className="absolute top-4 left-4">
                        <ArrowLeft className="h-6 w-6 mt-2 cursor-pointer" onClick={() => router.back()} />
                    </div>
                    <h1 className="text-lg font-medium mt-1">My credit cards</h1>
                </div>

                <div className="flex flex-col p-6 space-y-6 flex-1">
                    {/* Credit Card Display */}
                    <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                        <div className="space-y-4">
                            {/* Card Number Section */}
                            <div>
                                <h3 className="text-teal-500 text-sm font-medium mb-2">Credit card number</h3>
                                <div className="flex items-center justify-between">
                                    <span className="text-gray-400 text-lg tracking-wider">
                                        {formatCardNumber(creditCard.cardNumber)}
                                    </span>
                                    <div className="text-gray-400 font-bold text-lg">
                                        {getCardTypeIcon(creditCard.cardType)}
                                    </div>
                                </div>
                            </div>

                            {/* Expiry Date Section */}
                            <div>
                                <h3 className="text-teal-500 text-sm font-medium mb-2">Expiry date</h3>
                                <div className="flex items-center justify-between">
                                    <span className="text-gray-400 text-lg">
                                        {creditCard.expireDate}
                                    </span>
                                    {isCardExpired(creditCard.expireDate) && (
                                        <div className="flex items-center space-x-1">
                                            <span className="text-red-500 text-sm font-medium">Expired</span>
                                            <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                                                <span className="text-white text-xs font-bold">!</span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Manage Card Section */}
                    <div className="space-y-6">
                        <h2 className="text-lg font-medium text-gray-800">Manage card</h2>
                        
                        {/* Set as Default Card */}
                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <div className="flex items-center justify-between">
                                <span className="text-gray-700 font-medium">Set as default card</span>
                                <Switch.Root
                                    className="w-16 h-9 bg-gray-300 rounded-full relative data-[state=checked]:bg-primary"
                                    checked={creditCard.isDefault}
                                    onCheckedChange={handleDefaultCardToggle}
                                    disabled={isDefaultCardToggling}
                                >
                                    <Switch.Thumb className="block w-7 h-7 bg-white rounded-full transition-transform translate-x-1 data-[state=checked]:translate-x-8 data-[disabled]:opacity-40" />
                                </Switch.Root>
                            </div>
                        </div>

                        {/* Delete Card */}
                        <div className="bg-white rounded-lg border border-gray-200">
                            <button
                                onClick={handleDeleteCard}
                                className="w-full p-4 flex items-center space-x-3 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                            >
                                <Trash2 className="h-5 w-5" />
                                <span className="font-medium">Delete Card</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
