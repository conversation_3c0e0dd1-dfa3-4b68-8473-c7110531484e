'use client';

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { ArrowLeft, ArrowDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslation } from '@/contexts/LanguageContext';

interface TopUpData {
  fromCard: {
    id: string;
    cardNumber: string;
    cardType: string;
    isDefault: boolean;
  };
  toCard: {
    id: string;
    cardNumber: string;
    balance: string;
  };
  amount: string;
}

export default function ConfirmTopUpPage() {
  const { t } = useTranslation();
  const router = useRouter();

  // State management
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [pageLoading, setPageLoading] = useState(true);

  // Mock top-up data - replace with actual data from previous page
  const [topUpData] = useState<TopUpData>({
    fromCard: {
      id: '1',
      cardNumber: '8366',
      cardType: 'mastercard',
      isDefault: true
    },
    toCard: {
      id: '2',
      cardNumber: '6278 3792 4281',
      balance: '0.00'
    },
    amount: '350.00'
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const handleConfirm = async () => {
    setIsSubmitting(true);

    try {
      // Add your top-up confirmation API call here
      console.log('Confirming top-up:', topUpData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Navigate to success page
      router.push('/topup/success');

    } catch (error) {
      console.error('Top-up confirmation error:', error);
      // Handle error - show error modal
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCardIcon = (cardType: string) => {
    switch (cardType.toLowerCase()) {
      case 'mastercard':
        return (
          <div className="w-16 h-10 bg-white rounded-lg border border-gray-200 flex items-center justify-center">
            <div className="flex items-center space-x-1">
              <div className="w-4 h-4 bg-red-500 rounded-full opacity-80"></div>
              <div className="w-4 h-4 bg-yellow-500 rounded-full opacity-80 -ml-2"></div>
            </div>
          </div>
        );
      case 'visa':
        return (
          <div className="w-16 h-10 bg-white rounded-lg border border-gray-200 flex items-center justify-center">
            <span className="text-blue-600 font-bold text-xs">VISA</span>
          </div>
        );
      default:
        return (
          <div className="w-16 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
            <span className="text-gray-600 text-xs">{cardType.toUpperCase()}</span>
          </div>
        );
    }
  };

  const getToCardIcon = () => {
    return (
      <div className="w-16 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
        <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
          <div className="w-3 h-3 bg-gray-800 rounded-full"></div>
        </div>
      </div>
    );
  };

  return (
    <>
      {pageLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation" />
          </div>
        </div>
      )}

      <div className="flex flex-col min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-[#DDF1F0] flex items-center justify-center p-4 relative text-gray-700">
          <div className="absolute top-4 left-4">
            <ArrowLeft className="h-6 w-6 mt-2 cursor-pointer" onClick={() => router.back()} />
          </div>
          <h1 className="text-lg font-medium mt-1">Confirm top up</h1>
        </div>

        <div className="flex flex-col p-6 space-y-8 flex-1">
          {/* From Card Section */}
          <div className="flex flex-col items-left space-y-4">
            <div className="flex flex-col items-left space-y-2">
              {getCardIcon(topUpData.fromCard.cardType)}
              <div className="text-left">
                <p className="text-gray-500 text-sm">
                  Credit card <span className="font-medium">{topUpData.fromCard.cardNumber}</span>
                </p>
                {topUpData.fromCard.isDefault && (
                  <span className="inline-block mt-1 px-3 py-1 bg-teal-100 text-teal-600 rounded-full text-xs font-medium">
                    Default
                  </span>
                )}
              </div>
            </div>

            {/* Arrow Down */}
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center">
              <ArrowDown className="h-6 w-6 text-teal-600" />
            </div>

            {/* To Card Section */}
            <div className="flex flex-col items-left space-y-2">
              {getToCardIcon()}
              <div className="text-left">
                <p className="text-gray-500 text-sm">{topUpData.toCard.cardNumber}</p>
                <p className="text-lg font-semibold">
                  {topUpData.toCard.balance} <span className="text-sm font-normal text-gray-500">THB</span>
                </p>
              </div>
            </div>
          </div>

          {/* Amount Section */}
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 text-lg">Amount</span>
              <span className="text-2xl font-semibold">
                {topUpData.amount} <span className="text-lg font-normal text-gray-500">THB</span>
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-white p-4 border-t border-gray-200">
          <Button
            onClick={handleConfirm}
            className="w-full bg-primary text-white py-6 text-lg rounded-lg disabled:opacity-50"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading animation" />
              </div>
            ) : (
              'Confirm'
            )}
          </Button>
        </footer>
      </div>
    </>
  );
}