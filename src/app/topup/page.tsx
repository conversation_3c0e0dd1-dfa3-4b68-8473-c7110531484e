'use client';

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { ArrowLeft, ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslation } from '@/contexts/LanguageContext';

interface TopUpFormData {
  selectedCard: string;
  amount: string;
  customAmount: string;
}

interface CreditCard {
  id: string;
  cardNumber: string;
  cardType: string;
  balance: string;
}

const PRESET_AMOUNTS = [300, 500, 1000, 2000, 5000, 10000];

export default function TopUpPage() {
  const { t } = useTranslation();
  const router = useRouter();

  // State management
  const [formData, setFormData] = useState<TopUpFormData>({
    selectedCard: '',
    amount: '',
    customAmount: ''
  });

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [pageLoading, setPageLoading] = useState(true);

  // Mock credit card data - replace with actual API call
  const [creditCards] = useState<CreditCard[]>([
    {
      id: '1',
      cardNumber: '2341 6534 2391',
      cardType: 'visa',
      balance: '0.00'
    }
  ]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Event handlers
  const handleAmountSelect = (amount: number) => {
    setFormData(prev => ({
      ...prev,
      amount: amount.toString(),
      customAmount: amount.toString()
    }));
  };

  const handleCustomAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, ''); // Only allow numbers
    setFormData(prev => ({
      ...prev,
      customAmount: value,
      amount: value
    }));
  };

  const handleCardSelect = (cardId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedCard: cardId
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Add your top-up API call here
      console.log('Top-up data:', formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Navigate to success page or show success message
      router.push('/topup/success');

    } catch (error) {
      console.error('Top-up error:', error);
      // Handle error - show error modal
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = formData.amount && parseInt(formData.amount) > 0;

  return (
    <>
      {pageLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation" />
          </div>
        </div>
      )}

      <div className="flex flex-col min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-[#DDF1F0] flex items-center justify-center p-4 relative text-gray-700">
          <div className="absolute top-4 left-4">
            <ArrowLeft className="h-6 w-6 mt-2 cursor-pointer" onClick={() => router.back()} />
          </div>
          <h1 className="text-lg font-medium mt-1">Top up</h1>
        </div>

        <div className="flex flex-col p-6 space-y-6 flex-1">
          {/* Top up to Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-800">Top up to</h2>

            {/* Credit Card Display */}
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-8 bg-gray-800 rounded flex items-center justify-center">
                    <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                      <div className="w-3 h-3 bg-gray-800 rounded-full"></div>
                    </div>
                  </div>
                  <div>
                    <p className="text-gray-500 text-sm">{creditCards[0]?.cardNumber}</p>
                    <p className="text-lg font-semibold">{creditCards[0]?.balance} <span className="text-sm font-normal">THB</span></p>
                  </div>
                </div>
                {/* <div className="w-6 h-6 rounded-full border-2 border-[#4FC3F7] bg-[#4FC3F7] flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div> */}
                <ChevronLeft className="h-5 w-5 ml-auto rotate-180 text-[#8AD8D4]" />
              </div>
            </div>
          </div>

          {/* Payment Method Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-800">Payment method</h2>

            <div
              className="bg-white rounded-lg p-4 border border-gray-200 cursor-pointer"
              onClick={() => handleCardSelect('1')}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-6 bg-gray-200 rounded flex items-center justify-center">
                    <div className="text-xs text-gray-500">💳</div>
                  </div>
                  <span className="text-gray-500">Please select your credit card</span>
                </div>
                <ChevronLeft className="h-5 w-5 ml-auto rotate-180 text-[#8AD8D4]" />
                {/* <div className="w-6 h-6 rounded-full border-2 border-[#4FC3F7] bg-[#4FC3F7] flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div> */}
              </div>
            </div>
          </div>

          {/* Enter Amount Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-800">Enter the amount</h2>

            {/* Custom Amount Input */}
            <div className="relative mt-10">
              <input
                type="text"
                value={formData.customAmount}
                onChange={handleCustomAmountChange}
                className="w-full bg-transparent text-2xl font-medium text-gray-800 border-0 border-b-2 border-gray-300 focus:border-orange-500 focus:outline-none pb-2 pr-16 transition-colors duration-200"
                inputMode="numeric"
                placeholder=" "
                id="amount-input"
              />
              <label
                htmlFor="amount-input"
                className={`absolute left-0 transition-all duration-200 pointer-events-none ${
                  formData.customAmount
                    ? 'top-0 text-sm text-orange-500 transform -translate-y-6'
                    : 'top-2 text-lg text-gray-400'
                }`}
              >
                Amount
              </label>
              <div className="absolute right-0 top-2">
                <span className="text-lg font-medium text-gray-600">THB</span>
              </div>
            </div>

            {/* Preset Amount Buttons */}
            <div className="grid grid-cols-3 gap-3">
              {PRESET_AMOUNTS.map((amount) => (
                <Button
                  key={amount}
                  variant="outline"
                  onClick={() => handleAmountSelect(amount)}
                  className={`h-12 rounded-full border-2 ${
                    formData.amount === amount.toString()
                      ? 'border-[#4FC3F7] bg-[#4FC3F7]/10 text-[#4FC3F7]'
                      : 'border-gray-300 text-gray-600 hover:border-gray-400'
                  }`}
                >
                  {amount.toLocaleString()}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-white p-4 border-t border-gray-200">
          <Button
            onClick={handleSubmit}
            className="w-full bg-quaternary text-white py-6 text-lg rounded-lg disabled:opacity-50"
            disabled={!isFormValid || isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading animation" />
              </div>
            ) : (
              'Next'
            )}
          </Button>
        </footer>
      </div>
    </>
  );
}