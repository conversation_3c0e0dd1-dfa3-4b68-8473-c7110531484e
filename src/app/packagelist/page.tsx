
'use client';
import React, { useEffect, useRef } from 'react';
import Link from "next/link";
import { ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';//CardHeader
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
// import { useRouter } from "next/navigation";
import { api } from "@/trpc/react";
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/contexts/LanguageContext';

// import { useSession } from "next-auth/react";

const bgImage = '/images/package/img-header.svg';

export default function PackageListScreen() {
  const { t } = useTranslation();
  // const router = useRouter();
  // const { showError } = useErrorModal();
  const formatNumber = (num: number) => num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `${date.getMonth() + 1}`.padStart(2, "0");
    const day = `${date.getDate()}`.padStart(2, "0");
    return `${day}/${month}/${year}`;
  };

  // const { data: session } = useSession();
  // console.log(session?.user.systemBrandCode)

  const handleGoBackClick = () => {
    window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
  };

  const handleGoToPackageDetail = (e: { preventDefault: () => void }, packageID: number) => {
    e.preventDefault();
    window.location.assign(`/packagedetail/${packageID}`);
  };

  const handleGoBackToLineLiff = () => {
    window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
  };

  const { data: packageList, isLoading, error } = api.package.getPackageList.useQuery();

  useEffect(() => {
    const setToken = async () => {
      if (packageList?.newAccessToken) {
        try {
          const res = await fetch('/api/set-token', {
            method: 'POST',
            credentials: 'include',
            body: JSON.stringify({ token: packageList.newAccessToken }),
          });
          const data = await res.json() as { message: string };
          console.log('[Set Token Success]', data);
        } catch (err) {
          console.error('[Set Token Error]', err);
        }
      }
    };

    void setToken();
  }, [packageList?.newAccessToken]);


  if (isLoading) {
    return <div className="flex flex-col w-full max-w-md mx-auto bg-white">

      <div className="h-40 p-4 bg-gray-300 relative animate-pulse">
        <div className="mt-6 ml-4">
          <div className="h-8 w-48 bg-gray-400 rounded mb-1 mt-14"></div>
          <div className="h-4 w-64 bg-gray-400 rounded opacity-90"></div>
        </div>
        <div className="absolute top-16 right-4 bg-gray-200 rounded-full">
          <div className="h-14 w-14 bg-gray-400 rounded-full"></div>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {[1, 2].map((item) => (
          <div key={item} className="cursor-pointer">
            <div className="overflow-hidden border-0 shadow-xs rounded-lg mb-4 border border-gray-300 bg-gray-100 animate-pulse">
              <div className="relative">
                <div className="w-full h-40 bg-gray-300 rounded-lg"></div>
              </div>

              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <div className="h-6 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-6 bg-gray-300 rounded-2xl w-20"></div>
                </div>
                <div className="h-4 bg-gray-300 rounded w-full mb-1"></div>
                <div className="space-y-1">
                  <div className="h-4 bg-gray-300 rounded w-2/3 mt-2"></div>
                  <div className="relative w-full mt-2">
                    <div className="h-3 bg-gray-400 w-full rounded"></div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-end p-4">
                <div>
                  <div className="h-3 bg-gray-300 rounded w-16 mb-1"></div>
                  <div className="h-6 bg-gray-300 rounded w-24"></div>
                </div>
                <div className="h-4 bg-gray-300 rounded w-32"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  }

  return (

    <div className="flex flex-col w-full max-w-md mx-auto bg-white">
      {/* {
      (isLoading) && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation"></Image>

          </div>
        </div>
        
      )} */}

      {/* Header */}
      {/* rounded-b-xl */}
      <div style={{ backgroundImage: `url(${bgImage})` }} className="h-40 p-4 text-white bg-cover relative">
        <div className="absolute top-4 left-4">
          <ArrowLeft className="h-6 w-6 mt-2" onClick={handleGoBackClick} />
        </div>
        <div className="mt-6 ml-4">
          <h1 className="text-[22px] font-bold mb-1 mt-14 text-primary">{t('coupon_packages')}</h1>
          <p className="text-sm opacity-90">{t('members_only')}</p>
        </div>
        <div className="absolute top-16 right-4 bg-white rounded-full">
          <div className="rounded-full">
            <Image src="/images/package/coupon-package.svg" width={60} height={60} alt="Coupon icon"></Image>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-4">

        {
          packageList?.data.data.list.map((item) => (

            <Link href="" onClick={(e) => handleGoToPackageDetail(e, item.packageID)}
              className='cursor-pointer' key={item.packageID} >
              <Card className="overflow-hidden border-0 shadow-xs rounded-lg mb-4 border border-gray-300 bg-gray-100">
                <div className="relative">

                  {
                    item.packageImage ?
                      <>
                        <img
                          src={item.packageImage}
                          alt="Steak meal"
                          className="w-full h-40 object-fill rounded-lg" />

                        {item.soldPercentage === 100 && (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="bg-black bg-opacity-70 text-white text-lg px-6 py-2 font-medium rounded-[20px]">
                              {t('sold_out')}
                            </div>
                          </div>
                        )}
                        <div className="absolute bottom-0 left-0">
                          <Image src="/images/package/coupon-icon.svg" width={60} height={60} alt="Coupon icon"></Image>
                        </div>
                      </>
                      :
                      <div className="w-full h-40 bg-gray-300 flex items-center justify-center rounded-lg">
                        <span className="text-gray-600">{t('no_image')}</span>
                        {item.soldPercentage === 100 && (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="bg-black bg-opacity-70 text-white text-lg px-6 py-2 font-medium rounded-[20px]">
                              {t('sold_out')}
                            </div>
                          </div>
                        )}
                      </div>
                  }

                </div>

                <CardContent className="p-3">
                  <div className="flex justify-between items-start mb-2">
                    <p className="font-semibold text-base text-primary line-clamp-1">{item.packageTitle}</p>
                    <Badge variant="outline" className="bg-[#CC5328] text-white rounded-2xl pt-0 pb-0 shrink-0 ml-2">
                      <Image src="/images/package/coupon-icon2.svg" width={25} height={25} alt="coupon icon"></Image>
                      &nbsp;<span className="font-medium text-sm"></span><span className="font-medium text-sm ml-1">X{item.totalCoupon}</span>
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500 mb-3 line-clamp-2">
                    {item.packageDetail}
                  </p>
                  {item.isShowProgressBar && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        {item.soldPercentage == 100 ? (
                          <span className="text-orange-700 text-xs">{t('all_sold_out')}</span>
                        ) : (
                          <span className="text-orange-700 text-xs">{t('going_fast_grab_yours_now')}</span>
                        )}
                        {/* <span className="text-orange-700 font-normal">Going fast, Grab yours now!</span> */}
                      </div>
                      <div className="relative w-full">
                        {item.soldPercentage == 100 ? (
                          <Progress value={0} className="h-3 bg-gray-300 w-full mt-2" />
                        ) : (
                          <Progress value={item.soldPercentage} className="h-3 bg-gray-300 w-full mt-2" />
                        )}

                        {item.soldPercentage == 100 ?
                          <Image className="absolute -top-3 transition-all duration-300"
                            style={{
                              left: `calc(${item.soldPercentage}% - 1.6rem)`
                            }}
                            src="/images/package/mascot.svg" width={35} height={35} alt="coupon icon">
                          </Image>
                          :
                          <Image className="absolute -top-3 transition-all duration-300"
                            style={{
                              left: `calc(${item.soldPercentage}% - 1rem + 0.2rem)`
                            }}
                            src="/images/package/fire-icon.svg" width={22} height={22} alt="fire icon">
                          </Image>
                        }

                      </div>
                    </div>
                  )}

                </CardContent>

                <CardFooter className="flex justify-between items-end pb-3 pl-5 pr-2">
                  {/* items-end */}
                  <div>
                    {
                      item.discountAmount != 0 && (
                        <span className="text-red-700 line-through text-sm block -ml-2">{formatNumber(item.originalPrice)} {t('thb')}</span>
                      )
                    }
                    <div className="text-lg font-[600] -ml-2 text-quaternary">
                      {formatNumber(item.originalPrice - item.discountAmount)} {t('thb')}
                    </div>
                  </div>

                  {item.nearlyExpirePeriodAlert > 0 ? (
                    (() => {
                      const currentDate = new Date();
                      const endDate = new Date(item.dateActiveEnd);
                      const daysLeft = Math.ceil((endDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));

                      return daysLeft <= item.nearlyExpirePeriodAlert ? (
                        <span className="text-orange-700 font-medium text-xs">{t('only_days_left', { daysLeft })}</span>
                      ) : (
                        <div className="text-gray-500 text-xs text-right">
                          <span className="text-gray-400">{t('available_until')} </span>
                          {formatDate(item.dateActiveEnd)}
                        </div>
                      );
                    })()
                  ) : (
                    <div className="text-gray-500 text-xs text-right">
                      <span className="text-gray-400">{t('available_until')} </span>
                      &nbsp;{formatDate(item.dateActiveEnd)}
                    </div>
                  )}

                </CardFooter>
              </Card>
            </Link>

          ))
        }

        {
          packageList?.data.data.list.length === 0 && (
            <div className="flex flex-col items-center justify-between bg-white p-4 mt-20">
              <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md pt-16">
                <div className="flex items-center justify-center mb-10">
                  <Image src="/images/package/no-package.svg" width={60} height={60} alt="Loading animation"></Image>
                </div>

                <div className="text-center space-y-4 mb-8">
                  <div className="space-y-1">
                    <p className="text-gray-400">{t('not_found_coupon_package')}</p>
                  </div>
                </div>
              </div>

              {/* <div className="w-full max-w-md space-y-4 mb-8">
                <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={handleGoBackToLineLiff}>
                  Go back to home page
                </Button>
              </div> */}
            </div>
          )

        }

        {error && (

          <div className="flex flex-col items-center justify-between bg-white p-4 mt-20">
            <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md pt-16">
              <div className="flex items-center justify-center mb-10">
                <Image src="/images/package/mascot-sad.svg" width={100} height={100} alt="Loading animation"></Image>
              </div>

              <div className="text-center space-y-4 mb-8">
                <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>

                <div className="space-y-1">
                  <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
                </div>
              </div>
            </div>

            <div className="w-full max-w-md space-y-4 mb-8">
              <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={handleGoBackToLineLiff}>
                {t('back_to_home')}
              </Button>
            </div>
          </div>
        )}

      </div>
    </div>
  );
};