'use client';

import { api } from "@/trpc/react";
import { useParams } from "next/navigation";
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useEffect } from "react";
import { useTranslation } from '@/contexts/LanguageContext';

export default function CheckChargeStatusPage() {

    const { t } = useTranslation();
    const params = useParams();
    const id = params.id as string;
    const { data: chargeData, isLoading, error } = api.checkcharge.getChargeStatus.useQuery({id});

    useEffect(() => {
        const handleChargeStatus = async () => {
            if (!chargeData) return;

            try {
                if (chargeData.newAccessToken) {
                    const res = await fetch('/api/set-token', {
                        method: 'POST',
                        credentials: 'include',
                        body: JSON.stringify({ token: chargeData.newAccessToken }),
                    });

                    const result = await res.json() as { message: string };
                    console.log('[Set Token]', result);
                }

                if (chargeData.data.code === 1) {
                    // console.log('Success:', chargeData.data.data.requestType);

                    if (chargeData.data.data.requestType === "CaptureCharge") {
                        window.location.assign('/mycard');
                    } else {
                        window.location.assign('/payment-success');
                    }
                } else {
                    window.location.assign('/transaction-cancel');
                }

            } catch (err) {
                console.error('[Handle Charge Status Error]', err);
                window.location.assign('/transaction-cancel');
            }
        };

        void handleChargeStatus();

    }, [chargeData]);

    if (isLoading) return <div>Loading...</div>;
    
    const handleGoBackToLineLiff = () => {
        window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
    };
    
    if(error){
        return (  
            <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
                <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
                    <div className="flex items-center justify-center mb-10">
                        <Image src="/images/package/mascot-sad.svg" width={100} height={100} alt="Loading animation"></Image>
                    </div>

                    <div className="text-center space-y-4 mb-8">
                        <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>

                        <div className="space-y-1">
                            <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
                            {/* <p className="text-gray-400">please try again</p> */}
                        </div>
                    </div>
                </div>

                <div className="w-full max-w-md space-y-4 mb-8">
                    <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={handleGoBackToLineLiff}>
                        Go back to home page
                    </Button>
                </div>
            </div>
        )
    }
    
    return ""
}