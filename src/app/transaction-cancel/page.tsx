'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useTranslation } from '@/contexts/LanguageContext';

const goToCheckout = () => {
  window.location.assign(`/checkout`);
};

const goToPackageList = () => {
  window.location.assign('/packagelist');
};

export default function PaymentSuccessPage () {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col items-center justify-between min-h-screen bg-white p-4">
      {/* Content at the top and center */}
      <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md pt-16">
        <div className="flex items-center justify-center mb-10">
            <Image src="/images/package/mascot-sad.svg" width={100} height={100} alt="Loading animation"></Image>
            
            {/* <Image src="/images/package/cancel-icon.svg" width={60} height={60} alt="Loading animation"></Image> */}
        </div>
        
        <div className="text-center space-y-4 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800">{t('transaction_canceled')}</h2>
          
          <div className="space-y-1">
            <p className="text-gray-400">
              {t('transaction_canceled_description')}
            </p>
            {/* <p className="text-gray-400">No changes have been made.</p>
            <p className="text-gray-400">Feel free to try again whenever you are ready</p> */}

          </div>
        </div>
      </div>
      
      {/* Buttons fixed at the bottom */}
      <div className="w-full max-w-md space-y-4 mb-8">
        <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={goToCheckout}>
          {t('try_again')}
        </Button>
        
        <Button variant="outline" className="w-full text-gray-500 py-6 border border-gray-300 rounded-md" onClick={goToPackageList}>
          {t('done')}
        </Button>
      </div>
    </div>
  );
};