'use client';

import { useEffect, useState } from "react";
import { api } from "@/trpc/react";
import Image from "next/image";
// import Link from "next/link";
import { ArrowLeft, PlusCircle, ChevronLeft, CreditCard } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from 'next/navigation';
import { useErrorModal } from "@/components/ErrorModalContext";
import { useTranslation } from '@/contexts/LanguageContext';
// import { zod } from "zod";

interface CardItem {
  cardID: number;
  cardNumber: string;
  cardType: string;
  expireDate: string;
  isDefault: boolean;
}

export default function CheckoutPaymentPage() 
{
  const { t } = useTranslation();
  const router = useRouter();
  const formatNumber = (num: number) => num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });

  const [packageId, setPackageId] = useState<string | null>(null);
  const [price, setPrice] = useState<string | null>(null);
  const [title, setTitle] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  
  const { showError } = useErrorModal();

  const params = useSearchParams();
  const pCardID = params.get("cardID");
  const pCardNumber = params.get("cardNumber");
  const pCardType = params.get("cardType");
  const pIsDefault = params.get("isDefault");
  // console.log(cardID)

  const goToAddCard = () => {
    window.location.assign("/addcard");
  };

  const goToMyCard = () => {
    window.location.assign("/mycard");
  };

  const goBackToPackageDetail = () => {
    window.location.assign(`/packagedetail/${packageId}`);
  };

  const handleGoBackToLineLiff = () => {
    window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
  };

  // เรียกใช้ข้อมูลจาก API
  const { data: cardList, isLoading, error } = api.card.getCreditCardList.useQuery();

  useEffect(() => {
    const storedPackageId = localStorage.getItem('packageId');
    const storedPrice = localStorage.getItem('price');
    const storedTitle = localStorage.getItem('title');

    setPackageId(storedPackageId);
    setPrice(storedPrice);
    setTitle(storedTitle);
  }, []);

  useEffect(() => {
    // console.log('Card List', cardList);
    if (cardList?.cards && cardList.cards.length > 0) {
      localStorage.setItem('noFirstCard', '1');
    } else {
      localStorage.setItem('noFirstCard', '0');
    }
  }, [cardList]);

  // Token update effect
  useEffect(() => {


    const setToken = async () => {
      if (cardList?.newAccessToken) {
        try {
          const res = await fetch('/api/set-token', {
            method: 'POST',
            credentials: 'include',
            body: JSON.stringify({ token: cardList.newAccessToken }),
          });
          const data = await res.json() as { message: string };
          console.log('[Set Token Success]', data);
        } catch (err) {
          console.error('[Set Token Error]', err);
        }
      }
    };
    void setToken();
  }, [cardList?.newAccessToken]);

  const packageLookupMutation = api.packagepurchase.packagePurchaseLookup.useMutation();
  const confirmPackageMutation = api.packagepurchase.packagePurchaseConfirm.useMutation();

  const handleBuyPackage = async () => {
    setIsSubmitting(true);

    const response = await packageLookupMutation.mutateAsync({ packageId: packageId! });
    // console.log('Lookup Response :', response)
    const defaultCard = cardList?.cards.find(card => card.isDefault);
    // console.log('Response newAccessToken client:',response.newAccessToken)
    if (response.newAccessToken) {
      await fetch('/api/set-token', {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({ token: response.newAccessToken }),
      })
        .then(res => res.json())
        .then(data => {
          console.log('[Set Token]', data);
        })
        .catch(err => console.error('[Set Token Error]', err));
    }
    // console.log(response.data.code)

    if (response.data.code === "1") {

      const referenceCode = response.data.data.referenceCode;
      let cardID = defaultCard?.cardID.toString();

      if(pCardID){
        cardID = pCardID;
      }
      // console.log('CardID',cardID)
      const confirmResponse = await confirmPackageMutation.mutateAsync({ referenceCode, cardID: cardID ?? "" });

      if (confirmResponse.newAccessToken) {
        await fetch('/api/set-token', {
          method: 'POST',
          credentials: 'include',
          body: JSON.stringify({ token: confirmResponse.newAccessToken }),
        })
          .then(res => res.json())
          .then(data => {
            console.log('[Set Token]', data);
          })
          .catch(err => console.error('[Set Token Error]', err));
      }

      // console.log("confirm Code Response :",confirmResponse.data.code);

      if (confirmResponse.data.code === "1") {
        if (confirmResponse.data.data.authorizeURI) {
          console.log('authorizeURI');
          // console.log(confirmResponse.data.data.authorizeURI);
          // window.location.assign('/payment-success');
          window.location.href = confirmResponse.data.data.authorizeURI;
        } else {
          console.log('authorizeURI is null');
          console.log(confirmResponse.data.data.authorizeURI)
          window.location.assign('/payment-success');
        }

      } else {
        // console.log('Code::',confirmResponse.data.code);
        setIsSubmitting(false);

        showError({
          title: t('unable_to_process_payment'),
          description: t('check_your_payment_details'),
          showRedirectButton: false,
          redirectButtonText: t('done'),
          redirectTo: `/packagedetail/${packageId}`,
          cancelButtonText: '',
        });
      }

        // setModalOpen(true);

    }
    // } else {
    //   // setModalOpen(true);
    //   showError({
    //     title: "Error!",
    //     description: "Something went wrong. Please try again.",
    //     redirectTo: `/packagedetail/${packageId}`,
    //     showRedirectButton: true,
    //     redirectButtonText: "Back",
    //     cancelButtonText: "Cancel",
    //   });
    //   setIsSubmitting(false);
    // }

    // packagePurchaseLookup({ packageId: packageId as string });

  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
        <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
          <div className="flex items-center justify-center mb-10">
            <Image src="/images/package/mascot-sad.svg" width={100} height={100} alt="Loading animation"></Image>
          </div>

          <div className="text-center space-y-4 mb-8">
            <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>

            <div className="space-y-1">
              <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
              {/* <p className="text-gray-400">please try again</p> */}
            </div>
          </div>
        </div>

        <div className="w-full max-w-md space-y-4 mb-8">
          <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={handleGoBackToLineLiff}>
            {t('back_to_home')}
          </Button>
        </div>
      </div>
    )
  }


  if (isLoading) {
    return <div className="flex flex-col min-h-screen bg-white animate-pulse">
      {/* Header Skeleton */}
      <div className="flex items-center justify-center p-4 relative">
        <div className="absolute top-4 left-4">
          <div className="w-6 h-6 bg-gray-200 rounded-full" />
        </div>
        <div className="h-7 w-24 bg-gray-200 rounded mt-1" />
      </div>

      <div className="px-4 flex-1 overflow-auto">
        {/* Summary Section Skeleton */}
        <div className="mb-6 mt-10">
          <div className="h-7 w-32 bg-gray-200 rounded mb-4" />
          <div className="flex justify-between items-center mb-1">
            <div>
              <div className="h-6 w-48 bg-gray-200 rounded mb-2" />
              <div className="h-4 w-32 bg-gray-200 rounded" />
            </div>
            <div className="h-6 w-24 bg-gray-200 rounded" />
          </div>
        </div>

        <hr className="border-t border-gray-120 my-6" />

        {/* Payment Method Section Skeleton */}
        <div className="h-7 w-40 bg-gray-200 rounded mb-4" />

        {/* Card Skeleton */}
        <div className="mb-4 border rounded-lg p-3">
          <div className="flex items-center">
            <div className="w-10 h-8 bg-gray-200 rounded-md mr-3" />
            <div className="h-6 w-48 bg-gray-200 rounded" />
            <div className="ml-3 h-6 w-16 bg-gray-200 rounded-full" />
          </div>
        </div>

        {/* Link Skeleton */}
        <div className="flex items-center py-2">
          <div className="w-6 h-6 bg-gray-200 rounded-full mr-2" />
          <div className="h-5 w-32 bg-gray-200 rounded" />
          <div className="w-5 h-5 bg-gray-200 rounded-full ml-auto" />
        </div>
      </div>

      {/* Footer Skeleton */}
      <footer className="fixed bottom-0 left-0 w-full bg-gray-50 p-4 flex justify-between items-center">
        <div>
          <div className="h-4 w-16 bg-gray-200 rounded mb-1" />
          <div className="h-8 w-32 bg-gray-200 rounded" />
        </div>
        <div className="h-14 w-32 bg-gray-200 rounded" />
      </footer>
    </div>
  }

  // if (error) {
  //   return (
  //     <div className="flex flex-col items-center justify-center min-h-screen bg-white px-4 text-center">
  //       <h1 className="text-3xl font-medium mb-4 text-red-600">Something went wrong</h1>
  //       <p className="text-gray-500 mb-6">Please try again later.</p>
  //       <Button
  //         onClick={() => router.push('/packagelist')}
  //         className="bg-quaternary text-white hover:bg-green-700"
  //       >
  //         Go back to Package List
  //       </Button>
  //     </div>
  //   );
  // }

  return (
    <>
      {/* {isLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation"></Image>
          </div>
        </div>
      ) } */}
      <div className="flex flex-col min-h-screen bg-white">
        {/* Header */}
        <div className="flex items-center justify-center p-4 relative text-gray-700">
          <div className="absolute top-4 left-4">
            <ArrowLeft className="h-6 w-6 mt-2" onClick={goBackToPackageDetail} />
          </div>
          <h1 className="text-base font-medium mt-1">{t('check_out')}</h1>
        </div>

        <div className="px-4 flex-1 overflow-auto">
          {/* Summary Section */}
          <div className="mb-6 mt-10">
            <h2 className="text-sm font-medium mb-4">{t('summary')}</h2>
            <div className="flex justify-between items-center mb-1">
              <div>
                <p className="text-sm font-medium">{title}</p>
                <p className="text-gray-400 text-xs">{t('coupon_packages')}</p>
              </div>
              <p className="text-sm min-w-[120px] text-right self-start">{formatNumber(price ? parseFloat(price) : 0)} {t('thb')}</p>
            </div>
          </div>

          <hr className="border-t border-gray-120 my-6" />
          {/* Payment Method Section */}
          <h2 className="text-sm font-medium mb-4">{t('payment_method')}</h2>

          
          {pCardID ? (
          <>
            <Card className="mb-4 border rounded-lg">
              <CardContent className="p-3 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-10 h-8 rounded-md flex items-center justify-center mr-3">
                    <Image
                      src={`/images/credit-card/${pCardType?.toLowerCase()}.png`}
                      alt={pCardType ?? "Credit Card"}
                      width={40}
                      height={40}
                      className="rounded"
                    />
                  </div>
                  <span className="text-sm">{pCardNumber}</span>
                  {pIsDefault === "true" && (
                    <span className="ml-3 px-3 py-1 bg-green-100 text-green-600 rounded-full text-xs">
                      {t('default_card')}
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>

            {cardList?.cards && cardList.cards.length > 0 ? (
              <div
                className="flex items-center py-2 hover:bg-gray-100 rounded-md transition-colors mb-30 text-sm"
                onClick={goToMyCard}
              >
                <CreditCard className="h-6 w-6 mr-2 text-green-600" />
                {t('pay_with_other_card')}
                <ChevronLeft className="h-5 w-5 ml-auto rotate-180" />
              </div>
            ) : (
              <div
                className="flex items-center py-2 hover:bg-gray-100 rounded-md transition-colors text-sm"
                onClick={goToAddCard}
              >
                <PlusCircle className="h-6 w-6 mr-2 text-green-600" />
                {t('add_credit_debit_card')}
                <ChevronLeft className="h-5 w-5 ml-auto rotate-180" />
              </div>
            )}
          </>
        ) : (
          <>
            {cardList?.cards && cardList.cards.length > 0 ? (
              <>
                {cardList.cards
                  .filter((card: CardItem) => card.isDefault)
                  .map((card: CardItem, index: number) => (
                    <Card key={index} className="mb-4 border rounded-lg">
                      <CardContent className="p-3 flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-10 h-8 rounded-md flex items-center justify-center mr-3">
                            <Image
                              src={`/images/credit-card/${card.cardType.toLowerCase()}.png`}
                              alt={card.cardType}
                              width={40}
                              height={40}
                              className="rounded"
                            />
                          </div>
                          <span className="text-sm">{card.cardNumber}</span>
                          {card.isDefault && (
                            <span className="ml-3 px-3 py-1 bg-green-100 text-green-600 rounded-full text-xs">
                              {t('default_card')}
                            </span>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                <div
                  className="flex items-center py-2 hover:bg-gray-100 rounded-md transition-colors mb-30 text-sm"
                  onClick={goToMyCard}
                >
                  <CreditCard className="h-6 w-6 mr-2 text-green-600" />
                  {t('pay_with_other_card')}
                  <ChevronLeft className="h-5 w-5 ml-auto rotate-180" />
                </div>
              </>
            ) : (
              <div
                className="flex items-center py-2 hover:bg-gray-100 rounded-md transition-colors text-sm"
                onClick={goToAddCard}
              >
                <PlusCircle className="h-6 w-6 mr-2 text-green-600" />
                {t('add_credit_debit_card')}
                <ChevronLeft className="h-5 w-5 ml-auto rotate-180" />
              </div>
            )}
          </>
        )}


          {/* {cardList?.cards && cardList.cards.length > 0 ? (
            <>
              <div>
                {cardID && (
                  <Card key={index} className="mb-4 border rounded-lg">
                      <CardContent className="p-3 flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-10 h-8 rounded-md flex items-center justify-center mr-3">
                            <Image
                              src={`/images/credit-card/${(card.cardType).toLowerCase()}.png`}
                              alt="MasterCard"
                              width={40}
                              height={40}
                              className="rounded" />
                          </div>
                          <span className="text-lg">{card.cardNumber}</span>
                          {card.isDefault && (
                            <span className="ml-3 px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">Default</span>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                ) : 
                (
                  <>
                    {cardList.cards.filter(card => card.isDefault).map((card: CardItem, index) => (
                      <Card key={index} className="mb-4 border rounded-lg">
                        <CardContent className="p-3 flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-10 h-8 rounded-md flex items-center justify-center mr-3">
                              <Image
                                src={`/images/credit-card/${(card.cardType).toLowerCase()}.png`}
                                
                                alt="MasterCard"
                                width={40}
                                height={40}
                                className="rounded" />
                            </div>
                            <span className="text-lg">{card.cardNumber}</span>
                            {card.isDefault && (
                              <span className="ml-3 px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">Default</span>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </>
                )};
                
              </div>
              <div className="flex items-center py-2 hover:bg-gray-100 rounded-md transition-colors mb-30" onClick={goToMyCard}>
                <CreditCard className="h-6 w-6 mr-2 text-green-600" />
                Pay with other card
                <ChevronLeft className="h-5 w-5 ml-auto rotate-180" />
              </div>
            </>
          ) : (
            <div className="flex items-center py-2 hover:bg-gray-100 rounded-md transition-colors" onClick={goToAddCard}>
              <PlusCircle className="h-6 w-6 mr-2 text-green-600" />
              Add Credit/Debit card
              <ChevronLeft className="h-5 w-5 ml-auto rotate-180" />
            </div>
          )} */}
        </div>

        {/* Footer */}
        <footer className="fixed bottom-0 left-0 w-full bg-gray-50 p-4 flex justify-between items-center">
          <div>
            <p className="text-[10px] text-gray-500">{t('total')}</p>
            <p className="text-[22px] font-bold">{formatNumber(price ? parseFloat(price) : 0)} {t('thb')}</p>
          </div>
          <Button
            className="px-8 py-6 bg-[#D1C428] hover:bg-[#D1C428] text-white"
            disabled={!cardList?.cards || cardList.cards.length === 0 || isSubmitting}
            onClick={() => cardList?.cards && cardList.cards.length > 0 && handleBuyPackage()}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center w-20">
                <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading animation" />
              </div>
            ) : (
              t('buy_package')
            )}

          </Button>
        </footer>
      </div>
    </>
  );
}