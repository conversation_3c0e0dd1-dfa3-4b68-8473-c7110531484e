import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

interface ChargeStatusResponse {
 code: number;
  data: {
    
    paymentTransactionID: number;
    requestType: string;
  };
  newAccessToken: string;
}

export const checkchargeRouter = createTRPCRouter({
    getChargeStatus: protectedProcedure
      .input(z.object({ id: z.string() })) // ✅ เพิ่ม input schema
      .query(async ({ ctx, input }) => {

    const accessToken = ctx.accessToken; 
    // console.log('Access Token CC:', accessToken);
    // Get access token from context
    const chargeId = input.id;
    try {
      const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/payment/charges`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            chargeID: parseInt(chargeId)
        })
      });
      const newAccessToken = response.headers.get('New-Access-Token');

      const data = await response.json() as ChargeStatusResponse;
      // console.log('Data response checkcharge:', data);

      if(!response.ok){
        // console.error("Failed to get charge status:", await response.text());
        throw new Error('Failed to get charge status');
      }

      return {
        data,
        newAccessToken
      };
    } catch (err) {
      console.error('Get charge status failed:', err);
      throw new Error('An error occurred while fetching the charge status.');
    }
  })
});