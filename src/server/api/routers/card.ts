import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";


const cardSchema = z.object({
  name: z.string(),
  number: z.string(),
  expiration_month: z.number(),
  expiration_year: z.number(),
  security_code: z.string()
});

interface CardResponse {
  data: {
    cards: {
      cardID: number;
      cardNumber: string;
      cardType: string;
      expireDate: string;
      isDefault: boolean;
    }[];
  },
  newAccessToken: string;
}

interface defaultCardResponse {
  code: number;
  httpStatus: number;
  message: string;
  newAccessToken: string;
}

interface AddCreditCardResponse {
  code: string;
  data: {
    authorizeURI: string;
  };
  httpStatus: number;
  message: string;
  serverTime: string;
  reference: string;
}

export const cardRouter = createTRPCRouter({
  createToken:
    protectedProcedure.input(cardSchema).mutation(async ({ input }) => {
      const token = btoa(`${process.env.OMISE_SECRET_KEY}:`);

      const response = await fetch('https://vault.omise.co/tokens', {
        method: 'POST',
        headers: {
          Authorization: `Basic ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          card: input
        })
      });

      // const data = await response.json();
      const data = await response.json() as { id: string; message?: string };
      if (!response.ok) {
        throw new Error(data.message ?? 'Failed to create token');
      }
      return data;
    }),
  addCreditCard: protectedProcedure.input(
    z.object({
      token: z.string(),
      isDefault: z.boolean(),
      noFirstCard: z.string(),
    })
  ).mutation(async ({ ctx, input }) => {
    const API_ENDPOINT = `${process.env.MINOR_API_BASE_URL}/webview/payment/creditcard/add`;

    if(input.noFirstCard === '0'){
      input.isDefault = true;
    }
    try {
      const res = await fetch(API_ENDPOINT, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${ctx.accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenID: input.token,
          isDefault: input.isDefault,
        }),
      });

      // const data = await res.json();
      const data = await res.json() as AddCreditCardResponse;

      const newAccessToken = res.headers.get('New-Access-Token');

      if (!res.ok) {
        console.log('Error response:', res);
        throw new Error('Failed to add credit card');

        // throw new Error(data.message || 'Failed to add credit card');
      }

      return { data, newAccessToken };
    } catch (err) {
      console.error('Add credit card failed:', err);
      throw new Error('An error occurred while adding the credit card.');
    }
  }),

  getCreditCardList: protectedProcedure.query(async ({ ctx }) => {
    // const accessToken = ctx.session.user.accessToken;
    const accessToken = ctx.accessToken;

    try {
      const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/payment/creditcard`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
      });
      const data = await response.json() as CardResponse;;

      console.log('Data response:', data);
      const newAccessToken = response.headers.get('New-Access-Token');
      // console.log('New Access token:', newAccessToken);

      if (!response.ok) {
        console.log('Error response:', response);
        // data.message ?? 
        throw new Error('Failed to get credit card list');
      }

      return {
        cards: data.data?.cards || [], newAccessToken
      };
    } catch (error) {
      console.error('Fetch error:', error);
      throw new Error('Failed to fetch credit card list');
    }
  }),
  updateDefaultCreditCard: protectedProcedure.input(z.object({ cardID: z.number() })).mutation(async ({ ctx, input }) => {
    
    const accessToken = ctx.accessToken;
    try {
      const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/payment/creditcard/default`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cardID: input.cardID,
        }),
      });

      const newAccessToken = response.headers.get('New-Access-Token');
      const data = await response.json() as defaultCardResponse;
      console.log('Data response:', data);

      if (!response.ok) {
        throw new Error('Failed to update default card');
      }

      return { data, newAccessToken };

    } catch (error) {
      console.error('Update default card failed:', error);
      throw new Error('An error occurred while updating the default card.');
    }
  }),
});