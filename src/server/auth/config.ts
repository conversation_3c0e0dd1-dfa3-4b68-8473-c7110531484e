import { type DefaultSession, type NextAuthConfig } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { z } from "zod";
// import { type JWT } from "next-auth/jwt";
import "next-auth/jwt";
import { createMinorApi } from "../libs/client/minor-api-client";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      accessToken: string;
      systemBrandCode: string;
      contentLanguage: string;
      // ...other properties
      // role: UserRole;
    } & DefaultSession["user"];
  }

  interface User {
    accessToken: string;
    systemBrandCode: string;
    contentLanguage: string;
    // ...other properties
    // role: UserRole;
  }
}

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    accessToken: string;
    systemBrandCode: string;
    contentLanguage: string;
  }
}

const tokenCredentialSchema = z.object({
  token: z.string().nullable(),
  langCode: z.string().nullable(),
});

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
// let verifyInProgress = false;
export const authConfig = {
  
  providers: [
    CredentialsProvider({
      id: "token-auth",
      name: "Token Authentication",
      type: "credentials",
      credentials: {
        token: { type: "text" },
        langCode: { type: "text" },
      },
      
      async authorize(credentials) {
        console.log('⏰ Authorize function called at:', new Date().toISOString());
        const minorApi = createMinorApi();
        const parsedCredentials = tokenCredentialSchema.parse(credentials);
        
        const token = parsedCredentials?.token;
        const langCode = parsedCredentials?.langCode ?? 'en';

        if (!token) return null;

        // if (verifyInProgress) return null;
        // verifyInProgress = true;
        try {
          console.log("🔑 Token before verify:", token);
          const userData = await minorApi.verifyToken({ token: token, langCode: langCode });
          console.log("✅ Response from verify token :",userData)
          return {
            accessToken: userData.accessToken,
            systemBrandCode: 'TCC',//userData.systemBrandCode,
            contentLanguage: userData.contentLanguage,
          };

          // const mockUser = {
          //   accessToken: " ",
          //   systemBrandCode: "SZ"
          // };
          // return mockUser;

        } catch (error) {
          console.error("❌ Token verification error:", error);
          return null;
        }
      },
    }),
    /**
     * ...add more providers here.
     *
     * Most other providers require a bit more work than the Discord provider. For example, the
     * GitHub provider requires you to add the `refresh_token_expires_in` field to the Account
     * model. Refer to the NextAuth.js docs for the provider you want to use. Example:
     *
     * @see https://next-auth.js.org/providers/github
     */
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.accessToken = user.accessToken;
        token.systemBrandCode = user.systemBrandCode;
        token.contentLanguage = user.contentLanguage;
      }
      return token;
    },
    session: ({ session, token }) => {
      return {
        ...session,
        user: {
          ...session.user,
          accessToken: token.accessToken,
          systemBrandCode: token.systemBrandCode,
          contentLanguage: token.contentLanguage
        },
      };
    },
  },
  pages: {
    signIn: "/auth/signin",
    signOut: "/auth/signout",
    error: "/auth/error",
  },
} satisfies NextAuthConfig;