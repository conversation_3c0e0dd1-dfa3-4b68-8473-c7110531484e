<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Frame</title>
    <!-- Bootstrap 3 CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border: 2px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .top-section {
            display: flex;
            min-height: 400px;
        }
        
        .left-panel {
            flex: 1;
            border-right: 2px solid #ddd;
            padding: 20px;
            background-color: #fafafa;
        }
        
        .right-panel {
            flex: 1;
            padding: 20px;
            background-color: #fafafa;
        }
        
        .bottom-section {
            border-top: 2px solid #ddd;
            padding: 20px;
            min-height: 150px;
            background-color: #fafafa;
        }
        
        /* Alternative using Bootstrap grid */
        .bootstrap-layout {
            margin-top: 30px;
        }
        
        .panel-box {
            border: 2px solid #ddd;
            border-radius: 4px;
            min-height: 400px;
            padding: 20px;
            background-color: #fafafa;
        }
        
        .bottom-box {
            border: 2px solid #ddd;
            border-radius: 4px;
            min-height: 150px;
            padding: 20px;
            background-color: #fafafa;
            margin-top: 15px;
        }
        
        .section-title {
            color: #666;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .content-placeholder {
            color: #999;
            font-style: italic;
            text-align: center;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <!-- Method 1: Flexbox Layout -->
    <div class="main-container">
        <div class="top-section">
            <div class="left-panel">
                <div class="section-title">Left Panel</div>
                <div class="content-placeholder">
                    Content goes here...
                </div>
            </div>
            <div class="right-panel">
                <div class="section-title">Right Panel</div>
                <div class="content-placeholder">
                    Content goes here...
                </div>
            </div>
        </div>
        <div class="bottom-section">
            <div class="section-title">Bottom Section</div>
            <div class="content-placeholder">
                Content goes here...
            </div>
        </div>
    </div>
    
    <!-- Method 2: Bootstrap Grid Layout -->
    <div class="container-fluid bootstrap-layout">
        <div class="row">
            <div class="col-md-6">
                <div class="panel-box">
                    <div class="section-title">Left Panel (Bootstrap)</div>
                    <div class="content-placeholder">
                        Content goes here...
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="panel-box">
                    <div class="section-title">Right Panel (Bootstrap)</div>
                    <div class="content-placeholder">
                        Content goes here...
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="bottom-box">
                    <div class="section-title">Bottom Section (Bootstrap)</div>
                    <div class="content-placeholder">
                        Content goes here...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 3 JS -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
</body>
</html>
