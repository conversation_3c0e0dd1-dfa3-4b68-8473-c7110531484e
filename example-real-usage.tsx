'use client';

import { useSession } from "next-auth/react";
import { useAuth } from "@/hooks/useAuth";
import { api } from "@/trpc/react";

export default function MyComponent() {
  // วิธีที่ 1: ใช้ useSession โดยตรง
  const { data: session, status } = useSession();

  // วิธีที่ 2: ใช้ custom hook
  const { user, isAuthenticated, accessToken } = useAuth();

  // วิธีที่ 3: ใช้ใน tRPC query (session จะถูกส่งไปอัตโนมัติ)
  const { data: userData } = api.user.getProfile.useQuery(
    undefined,
    {
      enabled: isAuthenticated, // เรียก query เมื่อ authenticated แล้วเท่านั้น
    }
  );

  // ตัวอย่างการใช้ accessToken ใน API call
  const handleApiCall = async () => {
    if (!accessToken) {
      console.log("No access token available");
      return;
    }

    try {
      const response = await fetch("/api/some-endpoint", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      console.log("API Response:", data);
    } catch (error) {
      console.error("API Error:", error);
    }
  };

  // Loading state
  if (status === "loading") {
    return <div>Loading session...</div>;
  }

  // Not authenticated
  if (!isAuthenticated) {
    return <div>Please sign in to continue</div>;
  }

  return (
    <div className="p-6">
      <h1>User Dashboard</h1>
      
      {/* แสดงข้อมูล user */}
      <div className="mb-4">
        <h2>User Information:</h2>
        <p>Name: {user?.name}</p>
        <p>Email: {user?.email}</p>
        <p>Brand Code: {user?.systemBrandCode}</p>
        <p>Language: {user?.contentLanguage}</p>
      </div>

      {/* แสดงข้อมูลจาก API */}
      {userData && (
        <div className="mb-4">
          <h2>Profile Data:</h2>
          <pre>{JSON.stringify(userData, null, 2)}</pre>
        </div>
      )}

      {/* ปุ่มเรียก API */}
      <button 
        onClick={handleApiCall}
        className="bg-blue-500 text-white px-4 py-2 rounded"
      >
        Call API with Token
      </button>
    </div>
  );
}
